# -*- coding: utf-8 -*-
"""
طابع التقارير - مختبر الصحة العامة المركزي ذي قار
Reports Printer - Central Public Health Laboratory
"""

import os
import tempfile
from datetime import datetime
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, PageBreak
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

class ReportsPrinter:
    def __init__(self):
        """تهيئة طابع التقارير"""
        self.temp_dir = tempfile.gettempdir()
        self.setup_fonts()
        self.setup_styles()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي
            arabic_fonts = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
                "C:/Windows/Fonts/calibri.ttf"
            ]
            
            for font_path in arabic_fonts:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    break
            else:
                self.arabic_font = 'Helvetica'
                return
            
            self.arabic_font = 'Arabic'
            
        except Exception as e:
            print(f"خطأ في تحميل الخط العربي: {e}")
            self.arabic_font = 'Helvetica'
    
    def setup_styles(self):
        """إعداد أنماط النص"""
        self.styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Title'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.black
        )
        
        # نمط العنوان الفرعي
        self.subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=self.styles['Heading2'],
            fontName=self.arabic_font,
            fontSize=14,
            alignment=TA_CENTER,
            spaceAfter=15,
            textColor=colors.darkblue
        )
        
        # نمط النص العادي
        self.normal_style = ParagraphStyle(
            'CustomNormal',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_RIGHT,
            spaceAfter=6
        )
        
        # نمط النص المتوسط
        self.center_style = ParagraphStyle(
            'CustomCenter',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_CENTER
        )
    
    def print_report(self, report_data: list, filter_summary: list = None):
        """طباعة التقرير"""
        try:
            # إنشاء ملف PDF مؤقت
            pdf_file = os.path.join(self.temp_dir, f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
            
            # إنشاء المستند (أفقي للتقارير الكبيرة)
            doc = SimpleDocTemplate(
                pdf_file,
                pagesize=landscape(A4) if len(report_data) > 50 else A4,
                rightMargin=0.5*inch,
                leftMargin=0.5*inch,
                topMargin=0.5*inch,
                bottomMargin=0.5*inch
            )
            
            # بناء محتوى المستند
            story = []
            
            # إضافة الرأس
            self.add_header(story)
            
            # إضافة ملخص الفلاتر
            if filter_summary:
                self.add_filter_summary(story, filter_summary)
            
            # إضافة جدول التقرير
            self.add_report_table(story, report_data)
            
            # إضافة ملخص الإحصائيات
            self.add_report_statistics(story, report_data)
            
            # إضافة التذييل
            self.add_footer(story)
            
            # بناء المستند
            doc.build(story)
            
            # فتح الملف للطباعة
            os.startfile(pdf_file, "print")
            
            return pdf_file
            
        except Exception as e:
            print(f"خطأ في طباعة التقرير: {e}")
            raise e
    
    def export_to_pdf(self, report_data: list, filter_summary: list = None, file_path: str = None):
        """تصدير التقرير إلى PDF"""
        try:
            if not file_path:
                file_path = os.path.join(self.temp_dir, f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
            
            # إنشاء المستند
            doc = SimpleDocTemplate(
                file_path,
                pagesize=landscape(A4) if len(report_data) > 50 else A4,
                rightMargin=0.5*inch,
                leftMargin=0.5*inch,
                topMargin=0.5*inch,
                bottomMargin=0.5*inch
            )
            
            # بناء محتوى المستند
            story = []
            
            # إضافة الرأس
            self.add_header(story)
            
            # إضافة ملخص الفلاتر
            if filter_summary:
                self.add_filter_summary(story, filter_summary)
            
            # إضافة جدول التقرير
            self.add_report_table(story, report_data)
            
            # إضافة ملخص الإحصائيات
            self.add_report_statistics(story, report_data)
            
            # إضافة التذييل
            self.add_footer(story)
            
            # بناء المستند
            doc.build(story)
            
            return file_path
            
        except Exception as e:
            print(f"خطأ في تصدير التقرير: {e}")
            raise e
    
    def add_header(self, story):
        """إضافة رأس المستند"""
        # شعار وزارة الصحة (إذا توفر)
        try:
            logo_path = "ministry_logo.png"
            if os.path.exists(logo_path):
                logo = Image(logo_path, width=1*inch, height=1*inch)
                story.append(logo)
                story.append(Spacer(1, 10))
        except:
            pass
        
        # عنوان وزارة الصحة
        ministry_title = Paragraph("وزارة الصحة العراقية", self.title_style)
        story.append(ministry_title)
        
        # عنوان المختبر
        lab_title = Paragraph("مختبر الصحة العامة المركزي - ذي قار", self.subtitle_style)
        story.append(lab_title)
        
        # عنوان التقرير
        report_title = Paragraph("التقرير الشامل للمختبر", self.subtitle_style)
        story.append(report_title)
        
        story.append(Spacer(1, 20))
    
    def add_filter_summary(self, story, filter_summary):
        """إضافة ملخص الفلاتر"""
        if not filter_summary:
            return
        
        # عنوان الفلاتر
        filters_title = Paragraph("الفلاتر المطبقة:", self.subtitle_style)
        story.append(filters_title)
        
        # قائمة الفلاتر
        filters_text = "<br/>".join(filter_summary)
        filters_para = Paragraph(filters_text, self.normal_style)
        story.append(filters_para)
        
        story.append(Spacer(1, 15))
    
    def add_report_table(self, story, report_data):
        """إضافة جدول التقرير"""
        if not report_data:
            no_data = Paragraph("لا توجد بيانات للعرض", self.center_style)
            story.append(no_data)
            return
        
        # رأس الجدول
        table_data = [["ت", "الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", 
                      "جهة الإرسال", "التحليل", "النتيجة", "تاريخ السحب", "تاريخ النتيجة"]]
        
        # إضافة بيانات التقرير
        for i, record in enumerate(report_data, 1):
            gender_text = 'ذكر' if record['gender'] == 'M' else 'أنثى'
            result_text = self.translate_result(record['result']) if record['result'] else 'معلق'
            result_date = record['result_date'][:10] if record['result_date'] else ''
            
            table_data.append([
                str(i),
                str(record['national_number']),
                record['patient_name'],
                str(record['age']),
                gender_text,
                record['sample_type'],
                record['sending_entity'],
                record['test_name'],
                result_text,
                record['sample_collection_date'],
                result_date
            ])
        
        # إنشاء الجدول
        # تحديد عرض الأعمدة حسب حجم الصفحة
        if len(report_data) > 50:  # صفحة أفقية
            col_widths = [0.3*inch, 0.8*inch, 1.2*inch, 0.5*inch, 0.5*inch, 0.8*inch, 
                         1*inch, 1*inch, 0.7*inch, 0.8*inch, 0.8*inch]
        else:  # صفحة عمودية
            col_widths = [0.3*inch, 0.7*inch, 1*inch, 0.4*inch, 0.4*inch, 0.7*inch, 
                         0.9*inch, 0.9*inch, 0.6*inch, 0.7*inch, 0.7*inch]
        
        table = Table(table_data, colWidths=col_widths, repeatRows=1)
        
        # تطبيق التنسيق
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))
        
        # تلوين النتائج حسب النوع
        for i, record in enumerate(report_data, 1):
            row = i  # الصف في الجدول
            if record['result'] == 'Positive':
                table.setStyle(TableStyle([('BACKGROUND', (8, row), (8, row), colors.lightcoral)]))
            elif record['result'] == 'Negative':
                table.setStyle(TableStyle([('BACKGROUND', (8, row), (8, row), colors.lightgreen)]))
            elif record['result'] in ['Retest', 'Recollection']:
                table.setStyle(TableStyle([('BACKGROUND', (8, row), (8, row), colors.lightyellow)]))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    def add_report_statistics(self, story, report_data):
        """إضافة إحصائيات التقرير"""
        # عنوان الإحصائيات
        stats_title = Paragraph("إحصائيات التقرير", self.subtitle_style)
        story.append(stats_title)
        
        # حساب الإحصائيات
        total_records = len(report_data)
        
        # إحصائيات النتائج
        results_count = {}
        pending_count = 0
        
        for record in report_data:
            if record['result']:
                result = record['result']
                results_count[result] = results_count.get(result, 0) + 1
            else:
                pending_count += 1
        
        # إحصائيات الجنس
        male_count = len([r for r in report_data if r['gender'] == 'M'])
        female_count = len([r for r in report_data if r['gender'] == 'F'])
        
        # إحصائيات العمر
        ages = [r['age'] for r in report_data if r['age']]
        avg_age = sum(ages) / len(ages) if ages else 0
        
        # جدول الإحصائيات
        stats_data = [
            ["إجمالي السجلات", str(total_records)],
            ["النتائج المعلقة", str(pending_count)],
            ["الذكور", str(male_count)],
            ["الإناث", str(female_count)],
            ["متوسط العمر", f"{avg_age:.1f} سنة"]
        ]
        
        # إضافة إحصائيات النتائج
        for result, count in results_count.items():
            translated_result = self.translate_result(result)
            stats_data.append([translated_result, str(count)])
        
        stats_table = Table(stats_data, colWidths=[2*inch, 1*inch])
        stats_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        
        story.append(stats_table)
    
    def add_footer(self, story):
        """إضافة تذييل المستند"""
        # تاريخ ووقت الطباعة
        print_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        footer_text = f"تم إنشاء التقرير في: {print_time}"
        footer = Paragraph(footer_text, self.center_style)
        
        story.append(Spacer(1, 30))
        story.append(footer)
        
        # مساحة للتوقيعات
        signature_text = """
        <br/><br/>
        توقيع المسؤول: __________________ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; التاريخ: __________________
        <br/><br/>
        توقيع المدير: __________________ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; التاريخ: __________________
        """
        
        signature = Paragraph(signature_text, self.normal_style)
        story.append(signature)
    
    def translate_result(self, result):
        """ترجمة النتيجة إلى العربية"""
        translations = {
            'Positive': 'إيجابي',
            'Negative': 'سلبي',
            'Retest': 'إعادة فحص',
            'Recollection': 'إعادة سحب',
            'Sent': 'مُرسل',
            'TND': 'غير محدد'
        }
        return translations.get(result, result)
