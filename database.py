# -*- coding: utf-8 -*-
"""
قاعدة بيانات مختبر الصحة العامة المركزي ذي قار
Central Public Health Laboratory Database Manager
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

class DatabaseManager:
    def __init__(self, db_path: str = "lab_database.db"):
        """تهيئة مدير قاعدة البيانات"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # قراءة ملف SQL وتنفيذه
                if os.path.exists("database_schema.sql"):
                    with open("database_schema.sql", 'r', encoding='utf-8') as f:
                        schema = f.read()
                    conn.executescript(schema)
                else:
                    # إنشاء الجداول مباشرة إذا لم يوجد الملف
                    self._create_tables(conn)
                conn.commit()
                print("تم إنشاء قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
    
    def _create_tables(self, conn):
        """إنشاء الجداول مباشرة"""
        tables = [
            """CREATE TABLE IF NOT EXISTS sample_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS sending_entities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS tests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                national_number INTEGER NOT NULL UNIQUE,
                name TEXT NOT NULL,
                age INTEGER NOT NULL,
                gender TEXT NOT NULL CHECK (gender IN ('M', 'F')),
                address TEXT NOT NULL,
                phone TEXT NOT NULL,
                passport_number TEXT,
                receipt_number TEXT,
                sample_type_id INTEGER NOT NULL,
                sending_entity_id INTEGER NOT NULL,
                sample_collection_date DATE NOT NULL,
                sample_received_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                barcode TEXT NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sample_type_id) REFERENCES sample_types(id),
                FOREIGN KEY (sending_entity_id) REFERENCES sending_entities(id)
            )""",
            
            """CREATE TABLE IF NOT EXISTS patient_tests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER NOT NULL,
                test_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
                FOREIGN KEY (test_id) REFERENCES tests(id),
                UNIQUE(patient_id, test_id)
            )""",
            
            """CREATE TABLE IF NOT EXISTS batches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_number INTEGER NOT NULL UNIQUE,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                work_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS batch_samples (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_id INTEGER NOT NULL,
                patient_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE CASCADE,
                FOREIGN KEY (patient_id) REFERENCES patients(id),
                UNIQUE(batch_id, patient_id)
            )""",
            
            """CREATE TABLE IF NOT EXISTS batch_workers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_id INTEGER NOT NULL,
                test_id INTEGER NOT NULL,
                worker_name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE CASCADE,
                FOREIGN KEY (test_id) REFERENCES tests(id)
            )""",
            
            """CREATE TABLE IF NOT EXISTS results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER NOT NULL,
                test_id INTEGER NOT NULL,
                result TEXT NOT NULL CHECK (result IN ('Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND')),
                result_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
                FOREIGN KEY (test_id) REFERENCES tests(id),
                UNIQUE(patient_id, test_id)
            )"""
        ]
        
        for table in tables:
            conn.execute(table)
        
        # إدراج البيانات الأولية
        self._insert_initial_data(conn)
    
    def _insert_initial_data(self, conn):
        """إدراج البيانات الأولية"""
        # أنواع العينات
        sample_types = [
            ('دم',), ('بول',), ('براز',), ('مسحة',), ('بلغم',), ('سائل شوكي',)
        ]
        conn.executemany("INSERT OR IGNORE INTO sample_types (name) VALUES (?)", sample_types)
        
        # جهات الإرسال
        sending_entities = [
            ('مستشفى الناصرية العام',), ('مستشفى الحبوبي',), ('مستشفى الشطرة',),
            ('مركز صحي المدينة',), ('مركز صحي الشهداء',), ('عيادة خاصة',)
        ]
        conn.executemany("INSERT OR IGNORE INTO sending_entities (name) VALUES (?)", sending_entities)
        
        # التحاليل
        tests = [
            ('فحص كوفيد-19', 'فحص فيروس كورونا'),
            ('فحص الملاريا', 'فحص طفيلي الملاريا'),
            ('فحص السكري', 'فحص مستوى السكر في الدم'),
            ('فحص الكوليسترول', 'فحص مستوى الكوليسترول'),
            ('تحليل البول الكامل', 'فحص شامل للبول'),
            ('تحليل الدم الكامل', 'فحص شامل للدم')
        ]
        conn.executemany("INSERT OR IGNORE INTO tests (name, description) VALUES (?, ?)", tests)
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        return sqlite3.connect(self.db_path)
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """تنفيذ استعلام وإرجاع النتائج"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return []
    
    def execute_update(self, query: str, params: tuple = ()) -> bool:
        """تنفيذ استعلام تحديث"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute(query, params)
                conn.commit()
                return True
        except Exception as e:
            print(f"خطأ في تنفيذ التحديث: {e}")
            return False
    
    def get_next_national_number(self) -> int:
        """الحصول على الرقم الوطني التالي"""
        result = self.execute_query("SELECT MAX(national_number) as max_num FROM patients")
        if result and result[0]['max_num']:
            return result[0]['max_num'] + 1
        return 1
    
    def get_next_batch_number(self) -> int:
        """الحصول على رقم الوجبة التالي"""
        result = self.execute_query("SELECT MAX(batch_number) as max_num FROM batches")
        if result and result[0]['max_num']:
            return result[0]['max_num'] + 1
        return 1
    
    def generate_barcode(self, national_number: int) -> str:
        """إنتاج الباركود بناءً على الرقم الوطني"""
        return f"LAB{national_number:06d}"

    # دوال خاصة بأنواع العينات
    def get_sample_types(self) -> List[Dict]:
        """الحصول على جميع أنواع العينات"""
        return self.execute_query("SELECT * FROM sample_types ORDER BY name")

    def add_sample_type(self, name: str) -> bool:
        """إضافة نوع عينة جديد"""
        return self.execute_update("INSERT INTO sample_types (name) VALUES (?)", (name,))

    def update_sample_type(self, id: int, name: str) -> bool:
        """تحديث نوع عينة"""
        return self.execute_update("UPDATE sample_types SET name = ? WHERE id = ?", (name, id))

    def delete_sample_type(self, id: int) -> bool:
        """حذف نوع عينة"""
        return self.execute_update("DELETE FROM sample_types WHERE id = ?", (id,))

    # دوال خاصة بجهات الإرسال
    def get_sending_entities(self) -> List[Dict]:
        """الحصول على جميع جهات الإرسال"""
        return self.execute_query("SELECT * FROM sending_entities ORDER BY name")

    def add_sending_entity(self, name: str) -> bool:
        """إضافة جهة إرسال جديدة"""
        return self.execute_update("INSERT INTO sending_entities (name) VALUES (?)", (name,))

    def update_sending_entity(self, id: int, name: str) -> bool:
        """تحديث جهة إرسال"""
        return self.execute_update("UPDATE sending_entities SET name = ? WHERE id = ?", (name, id))

    def delete_sending_entity(self, id: int) -> bool:
        """حذف جهة إرسال"""
        return self.execute_update("DELETE FROM sending_entities WHERE id = ?", (id,))

    # دوال خاصة بالتحاليل
    def get_tests(self) -> List[Dict]:
        """الحصول على جميع التحاليل"""
        return self.execute_query("SELECT * FROM tests ORDER BY name")

    def add_test(self, name: str, description: str = "") -> bool:
        """إضافة تحليل جديد"""
        return self.execute_update("INSERT INTO tests (name, description) VALUES (?, ?)", (name, description))

    def update_test(self, id: int, name: str, description: str = "") -> bool:
        """تحديث تحليل"""
        return self.execute_update("UPDATE tests SET name = ?, description = ? WHERE id = ?", (name, description, id))

    def delete_test(self, id: int) -> bool:
        """حذف تحليل"""
        return self.execute_update("DELETE FROM tests WHERE id = ?", (id,))
