# -*- coding: utf-8 -*-
"""
مولد الباركود - مختبر الصحة العامة المركزي ذي قار
Barcode Generator - Central Public Health Laboratory
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import barcode
from barcode.writer import ImageWriter
from io import BytesIO
import tempfile

# للطباعة على Windows
try:
    import win32print
    import win32ui
    from PIL import ImageWin
    WINDOWS_PRINTING = True
except ImportError:
    WINDOWS_PRINTING = False

class BarcodeGenerator:
    def __init__(self):
        """تهيئة مولد الباركود"""
        self.temp_dir = tempfile.gettempdir()
        self.barcode_width = 300
        self.barcode_height = 100
        self.label_height = 40
        self.total_height = self.barcode_height + self.label_height
        
        # إعدادات الخط
        self.font_size = 16
        self.font_path = self.get_arabic_font()
    
    def get_arabic_font(self):
        """الحصول على خط عربي"""
        # قائمة الخطوط العربية المحتملة في Windows
        arabic_fonts = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/tahoma.ttf",
            "C:/Windows/Fonts/calibri.ttf",
            "arial.ttf",
            "tahoma.ttf"
        ]
        
        for font_path in arabic_fonts:
            if os.path.exists(font_path):
                return font_path
        
        return None
    
    def generate_barcode_image(self, barcode_text: str, patient_name: str = "") -> Image.Image:
        """إنتاج صورة الباركود مع النص"""
        try:
            # إنشاء الباركود
            code128 = barcode.get_barcode_class('code128')
            barcode_instance = code128(barcode_text, writer=ImageWriter())
            
            # إنتاج صورة الباركود
            barcode_buffer = BytesIO()
            barcode_instance.write(barcode_buffer, options={
                'module_width': 0.2,
                'module_height': 15,
                'quiet_zone': 6.5,
                'font_size': 10,
                'text_distance': 5,
                'background': 'white',
                'foreground': 'black'
            })
            
            # فتح صورة الباركود
            barcode_buffer.seek(0)
            barcode_img = Image.open(barcode_buffer)
            
            # تغيير حجم الباركود
            barcode_img = barcode_img.resize((self.barcode_width, self.barcode_height), Image.Resampling.LANCZOS)
            
            # إنشاء صورة جديدة تتضمن النص
            total_img = Image.new('RGB', (self.barcode_width, self.total_height), 'white')
            
            # لصق الباركود
            total_img.paste(barcode_img, (0, 0))
            
            # إضافة النص
            if patient_name:
                draw = ImageDraw.Draw(total_img)
                
                # تحميل الخط
                try:
                    if self.font_path:
                        font = ImageFont.truetype(self.font_path, self.font_size)
                    else:
                        font = ImageFont.load_default()
                except:
                    font = ImageFont.load_default()
                
                # حساب موضع النص
                text_bbox = draw.textbbox((0, 0), patient_name, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                
                text_x = (self.barcode_width - text_width) // 2
                text_y = self.barcode_height + (self.label_height - text_height) // 2
                
                # رسم النص
                draw.text((text_x, text_y), patient_name, fill='black', font=font)
            
            return total_img
            
        except Exception as e:
            print(f"خطأ في إنتاج الباركود: {e}")
            # إنشاء صورة بديلة في حالة الخطأ
            error_img = Image.new('RGB', (self.barcode_width, self.total_height), 'white')
            draw = ImageDraw.Draw(error_img)
            draw.text((10, 10), f"Error: {barcode_text}", fill='black')
            return error_img
    
    def save_barcode(self, barcode_text: str, patient_name: str = "", file_path: str = None) -> str:
        """حفظ الباركود كملف صورة"""
        if not file_path:
            file_path = os.path.join(self.temp_dir, f"barcode_{barcode_text}.png")
        
        try:
            barcode_img = self.generate_barcode_image(barcode_text, patient_name)
            barcode_img.save(file_path, 'PNG')
            return file_path
        except Exception as e:
            print(f"خطأ في حفظ الباركود: {e}")
            return None
    
    def print_barcode(self, barcode_text: str, patient_name: str = ""):
        """طباعة الباركود"""
        try:
            # إنتاج صورة الباركود
            barcode_img = self.generate_barcode_image(barcode_text, patient_name)
            
            if WINDOWS_PRINTING:
                self.print_on_windows(barcode_img, barcode_text)
            else:
                # حفظ الصورة وفتحها للطباعة
                temp_file = self.save_barcode(barcode_text, patient_name)
                if temp_file:
                    os.startfile(temp_file, "print")
                    
        except Exception as e:
            print(f"خطأ في طباعة الباركود: {e}")
            raise e
    
    def print_on_windows(self, image: Image.Image, barcode_text: str):
        """طباعة على Windows باستخدام win32print"""
        try:
            # الحصول على الطابعة الافتراضية
            printer_name = win32print.GetDefaultPrinter()
            
            # فتح الطابعة
            hprinter = win32print.OpenPrinter(printer_name)
            
            try:
                # بدء مهمة الطباعة
                hdc = win32ui.CreateDC()
                hdc.CreatePrinterDC(printer_name)
                
                # بدء الصفحة
                hdc.StartDoc(f"Barcode_{barcode_text}")
                hdc.StartPage()
                
                # تحويل الصورة للطباعة
                dib = ImageWin.Dib(image)
                
                # حساب موضع الطباعة (في الوسط)
                printer_size = hdc.GetDeviceCaps(8), hdc.GetDeviceCaps(10)  # HORZRES, VERTRES
                image_size = image.size
                
                # تحديد حجم الطباعة (بالبكسل)
                print_width = min(printer_size[0] // 3, image_size[0] * 2)
                print_height = int(print_width * image_size[1] / image_size[0])
                
                # موضع الطباعة
                x = (printer_size[0] - print_width) // 2
                y = (printer_size[1] - print_height) // 2
                
                # طباعة الصورة
                dib.draw(hdc.GetHandleOutput(), (x, y, x + print_width, y + print_height))
                
                # إنهاء الطباعة
                hdc.EndPage()
                hdc.EndDoc()
                
            finally:
                win32print.ClosePrinter(hprinter)
                
        except Exception as e:
            print(f"خطأ في الطباعة على Windows: {e}")
            raise e
    
    def get_available_printers(self) -> list:
        """الحصول على قائمة الطابعات المتاحة"""
        printers = []
        
        if WINDOWS_PRINTING:
            try:
                # الحصول على جميع الطابعات
                printer_info = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)
                
                for printer in printer_info:
                    printers.append({
                        'name': printer[2],
                        'is_default': printer[2] == win32print.GetDefaultPrinter()
                    })
                    
            except Exception as e:
                print(f"خطأ في الحصول على قائمة الطابعات: {e}")
        
        return printers
    
    def set_default_printer(self, printer_name: str) -> bool:
        """تعيين الطابعة الافتراضية"""
        if WINDOWS_PRINTING:
            try:
                win32print.SetDefaultPrinter(printer_name)
                return True
            except Exception as e:
                print(f"خطأ في تعيين الطابعة الافتراضية: {e}")
                return False
        return False
    
    def detect_barcode_printer(self) -> str:
        """التعرف التلقائي على طابعة الباركود"""
        printers = self.get_available_printers()
        
        # كلمات مفتاحية للبحث عن طابعات الباركود
        barcode_keywords = ['barcode', 'label', 'zebra', 'datamax', 'intermec', 'tsc', 'godex']
        
        for printer in printers:
            printer_name_lower = printer['name'].lower()
            for keyword in barcode_keywords:
                if keyword in printer_name_lower:
                    return printer['name']
        
        # إذا لم توجد طابعة باركود، إرجاع الطابعة الافتراضية
        for printer in printers:
            if printer['is_default']:
                return printer['name']
        
        return None
    
    def print_multiple_barcodes(self, barcodes_data: list):
        """طباعة عدة باركودات"""
        """
        barcodes_data: قائمة من القواميس تحتوي على:
        [{'barcode': 'LAB000001', 'name': 'أحمد محمد'}, ...]
        """
        try:
            for data in barcodes_data:
                self.print_barcode(data['barcode'], data.get('name', ''))
                
        except Exception as e:
            print(f"خطأ في طباعة الباركودات المتعددة: {e}")
            raise e
