# -*- coding: utf-8 -*-
"""
مختبر الصحة العامة المركزي ذي قار
Central Public Health Laboratory - Dhi Qar
التطبيق الرئيسي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime
from tkinter import font

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from data_entry_window import DataEntryWindow
from work_window import WorkWindow
from results_window import ResultsWindow
from reports_window import ReportsWindow
from settings_window import SettingsWindow

# استيراد الثيمات الحديثة
try:
    from tkinter import ttk
    import tkinter.ttk as ttk
except ImportError:
    pass

class LabMainApplication:
    def __init__(self):
        """تهيئة التطبيق الرئيسي"""
        self.root = tk.Tk()
        self.setup_main_window()
        self.db_manager = DatabaseManager()
        self.setup_menu()
        self.setup_main_interface()
        
        # النوافذ الفرعية
        self.data_entry_window = None
        self.work_window = None
        self.results_window = None
        self.reports_window = None
        self.settings_window = None
        
        # متغيرات للتزامن بين النوافذ
        self.observers = []
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("مختبر الصحة العامة المركزي - ذي قار")
        self.root.geometry("1400x900")
        self.root.state('zoomed')  # فتح النافذة بحجم كامل

        # تعيين الألوان والثيم الحديث
        self.setup_modern_theme()

        # تعيين الخطوط العصرية
        self.setup_fonts()

        # تعيين أيقونة التطبيق (إذا توفرت)
        try:
            self.root.iconbitmap('lab_icon.ico')
        except:
            pass

        # تعيين خلفية متدرجة
        self.root.configure(bg='#f0f2f5')

    def setup_modern_theme(self):
        """إعداد الثيم العصري"""
        style = ttk.Style()

        # الألوان العصرية
        self.colors = {
            'primary': '#2c3e50',      # أزرق داكن
            'secondary': '#3498db',    # أزرق فاتح
            'success': '#27ae60',      # أخضر
            'warning': '#f39c12',      # برتقالي
            'danger': '#e74c3c',       # أحمر
            'light': '#ecf0f1',        # رمادي فاتح
            'dark': '#2c3e50',         # رمادي داكن
            'white': '#ffffff',        # أبيض
            'background': '#f8f9fa',   # خلفية فاتحة
            'card': '#ffffff',         # خلفية البطاقات
            'border': '#dee2e6',       # حدود
            'text': '#2c3e50',         # نص
            'text_light': '#6c757d'    # نص فاتح
        }

        # تطبيق الثيم على العناصر
        style.theme_use('clam')

        # تخصيص الأزرار
        style.configure('Modern.TButton',
                       background=self.colors['secondary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10),
                       font=('Segoe UI', 11, 'bold'))

        style.map('Modern.TButton',
                 background=[('active', '#2980b9'),
                           ('pressed', '#21618c')])

        # تخصيص الأزرار الرئيسية
        style.configure('Primary.TButton',
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(25, 12),
                       font=('Segoe UI', 12, 'bold'))

        style.map('Primary.TButton',
                 background=[('active', '#34495e'),
                           ('pressed', '#2c3e50')])

        # تخصيص الأزرار الثانوية
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8),
                       font=('Segoe UI', 10, 'bold'))

        # تخصيص الإطارات
        style.configure('Card.TFrame',
                       background=self.colors['card'],
                       relief='flat',
                       borderwidth=1)

        # تخصيص التسميات
        style.configure('Heading.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['primary'],
                       font=('Segoe UI', 16, 'bold'))

        style.configure('Subheading.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=('Segoe UI', 12, 'normal'))

        # تخصيص الجداول
        style.configure('Modern.Treeview',
                       background=self.colors['white'],
                       foreground=self.colors['text'],
                       fieldbackground=self.colors['white'],
                       borderwidth=0,
                       font=('Segoe UI', 10))

        style.configure('Modern.Treeview.Heading',
                       background=self.colors['primary'],
                       foreground='white',
                       font=('Segoe UI', 11, 'bold'))

    def setup_fonts(self):
        """إعداد الخطوط العصرية"""
        # الخطوط الأساسية
        self.fonts = {
            'title': ('Segoe UI', 24, 'bold'),
            'subtitle': ('Segoe UI', 18, 'normal'),
            'heading': ('Segoe UI', 16, 'bold'),
            'subheading': ('Segoe UI', 14, 'normal'),
            'body': ('Segoe UI', 12, 'normal'),
            'small': ('Segoe UI', 10, 'normal'),
            'button': ('Segoe UI', 11, 'bold'),
            'arabic': ('Arial', 12, 'normal')
        }

        # تطبيق الخطوط على النظام
        default_font = font.nametofont("TkDefaultFont")
        default_font.configure(family='Segoe UI', size=11)

        text_font = font.nametofont("TkTextFont")
        text_font.configure(family='Segoe UI', size=11)
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_file)
        file_menu.add_command(label="فتح", command=self.open_file)
        file_menu.add_command(label="حفظ", command=self.save_file)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_application)
        
        # قائمة النوافذ
        windows_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="النوافذ", menu=windows_menu)
        windows_menu.add_command(label="إدخال البيانات", command=self.open_data_entry)
        windows_menu.add_command(label="العمل", command=self.open_work_window)
        windows_menu.add_command(label="النتائج", command=self.open_results_window)
        windows_menu.add_command(label="التقارير والإحصائيات", command=self.open_reports_window)
        windows_menu.add_command(label="الإعدادات", command=self.open_settings_window)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        help_menu.add_command(label="دليل المستخدم", command=self.show_user_guide)
    
    def setup_main_interface(self):
        """إعداد الواجهة الرئيسية العصرية"""
        # إطار رئيسي مع خلفية عصرية
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True)

        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # إنشاء Canvas للتمرير
        canvas = tk.Canvas(main_frame, bg=self.colors['background'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.colors['background'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # رأس عصري مع تدرج
        self.setup_modern_header(scrollable_frame)

        # بطاقات الوظائف الرئيسية
        self.setup_function_cards(scrollable_frame)

        # لوحة المعلومات السريعة
        self.setup_dashboard(scrollable_frame)

        # شريط الحالة العصري
        self.setup_modern_status_bar()

        # تخطيط Canvas
        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")

        # ربط عجلة الماوس بالتمرير
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def setup_modern_header(self, parent):
        """إعداد رأس عصري"""
        # إطار الرأس مع خلفية متدرجة
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=200)
        header_frame.pack(fill=tk.X, pady=(0, 30))
        header_frame.pack_propagate(False)

        # محتوى الرأس
        content_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        content_frame.pack(expand=True, fill=tk.BOTH, padx=40, pady=40)

        # العنوان الرئيسي
        title_label = tk.Label(content_frame,
                              text="مختبر الصحة العامة المركزي",
                              font=self.fonts['title'],
                              fg='white',
                              bg=self.colors['primary'])
        title_label.pack(anchor=tk.W)

        # العنوان الفرعي
        subtitle_label = tk.Label(content_frame,
                                 text="ذي قار - وزارة الصحة العراقية",
                                 font=self.fonts['subtitle'],
                                 fg='#bdc3c7',
                                 bg=self.colors['primary'])
        subtitle_label.pack(anchor=tk.W, pady=(5, 0))

        # معلومات سريعة في الرأس
        info_frame = tk.Frame(content_frame, bg=self.colors['primary'])
        info_frame.pack(anchor=tk.W, pady=(20, 0))

        # التاريخ والوقت
        current_time = datetime.now().strftime("%Y-%m-%d | %H:%M")
        time_label = tk.Label(info_frame,
                             text=f"📅 {current_time}",
                             font=self.fonts['body'],
                             fg='#ecf0f1',
                             bg=self.colors['primary'])
        time_label.pack(side=tk.LEFT, padx=(0, 30))

        # حالة النظام
        status_label = tk.Label(info_frame,
                               text="🟢 النظام يعمل بشكل طبيعي",
                               font=self.fonts['body'],
                               fg='#2ecc71',
                               bg=self.colors['primary'])
        status_label.pack(side=tk.LEFT)

    def setup_function_cards(self, parent):
        """إعداد بطاقات الوظائف الرئيسية"""
        # إطار البطاقات
        cards_frame = tk.Frame(parent, bg=self.colors['background'])
        cards_frame.pack(fill=tk.X, pady=(0, 30))

        # عنوان القسم
        section_title = tk.Label(cards_frame,
                                text="الوظائف الرئيسية",
                                font=self.fonts['heading'],
                                fg=self.colors['text'],
                                bg=self.colors['background'])
        section_title.pack(anchor=tk.W, pady=(0, 20))

        # إطار البطاقات
        cards_container = tk.Frame(cards_frame, bg=self.colors['background'])
        cards_container.pack(fill=tk.X)

        # تكوين الأعمدة
        for i in range(5):
            cards_container.columnconfigure(i, weight=1)

        # بيانات البطاقات
        cards_data = [
            {
                'title': 'إدخال البيانات',
                'icon': '📝',
                'description': 'إضافة مرضى وعينات جديدة',
                'command': self.open_data_entry,
                'color': self.colors['secondary']
            },
            {
                'title': 'العمل',
                'icon': '🔬',
                'description': 'إدارة وجبات العمل',
                'command': self.open_work_window,
                'color': self.colors['success']
            },
            {
                'title': 'النتائج',
                'icon': '📊',
                'description': 'إدخال وعرض النتائج',
                'command': self.open_results_window,
                'color': self.colors['warning']
            },
            {
                'title': 'التقارير',
                'icon': '📈',
                'description': 'تقارير وإحصائيات شاملة',
                'command': self.open_reports_window,
                'color': self.colors['danger']
            },
            {
                'title': 'الإعدادات',
                'icon': '⚙️',
                'description': 'إعدادات النظام',
                'command': self.open_settings_window,
                'color': self.colors['dark']
            }
        ]

        # إنشاء البطاقات
        for i, card_data in enumerate(cards_data):
            self.create_function_card(cards_container, card_data, i)

    def create_function_card(self, parent, card_data, column):
        """إنشاء بطاقة وظيفة واحدة"""
        # إطار البطاقة
        card_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=0)
        card_frame.grid(row=0, column=column, padx=10, pady=10, sticky='ew')

        # إضافة تأثير الظل
        shadow_frame = tk.Frame(parent, bg='#00000010', height=2)
        shadow_frame.grid(row=1, column=column, padx=12, pady=(0, 5), sticky='ew')

        # محتوى البطاقة
        content_frame = tk.Frame(card_frame, bg=self.colors['card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # الأيقونة
        icon_label = tk.Label(content_frame,
                             text=card_data['icon'],
                             font=('Segoe UI Emoji', 32),
                             bg=self.colors['card'],
                             fg=card_data['color'])
        icon_label.pack(pady=(0, 10))

        # العنوان
        title_label = tk.Label(content_frame,
                              text=card_data['title'],
                              font=self.fonts['subheading'],
                              bg=self.colors['card'],
                              fg=self.colors['text'])
        title_label.pack(pady=(0, 5))

        # الوصف
        desc_label = tk.Label(content_frame,
                             text=card_data['description'],
                             font=self.fonts['small'],
                             bg=self.colors['card'],
                             fg=self.colors['text_light'],
                             wraplength=150,
                             justify=tk.CENTER)
        desc_label.pack(pady=(0, 15))

        # الزر
        button = tk.Button(content_frame,
                          text="فتح",
                          font=self.fonts['button'],
                          bg=card_data['color'],
                          fg='white',
                          relief='flat',
                          bd=0,
                          padx=20,
                          pady=8,
                          cursor='hand2',
                          command=card_data['command'])
        button.pack()

        # تأثيرات التفاعل
        def on_enter(e):
            card_frame.configure(bg='#f8f9fa')
            content_frame.configure(bg='#f8f9fa')
            icon_label.configure(bg='#f8f9fa')
            title_label.configure(bg='#f8f9fa')
            desc_label.configure(bg='#f8f9fa')

        def on_leave(e):
            card_frame.configure(bg=self.colors['card'])
            content_frame.configure(bg=self.colors['card'])
            icon_label.configure(bg=self.colors['card'])
            title_label.configure(bg=self.colors['card'])
            desc_label.configure(bg=self.colors['card'])

        card_frame.bind("<Enter>", on_enter)
        card_frame.bind("<Leave>", on_leave)
        content_frame.bind("<Enter>", on_enter)
        content_frame.bind("<Leave>", on_leave)

    def setup_dashboard(self, parent):
        """إعداد لوحة المعلومات العصرية"""
        # إطار لوحة المعلومات
        dashboard_frame = tk.Frame(parent, bg=self.colors['background'])
        dashboard_frame.pack(fill=tk.X, pady=(0, 30))

        # عنوان القسم
        section_title = tk.Label(dashboard_frame,
                                text="لوحة المعلومات",
                                font=self.fonts['heading'],
                                fg=self.colors['text'],
                                bg=self.colors['background'])
        section_title.pack(anchor=tk.W, pady=(0, 20))

        # إطار الإحصائيات
        stats_container = tk.Frame(dashboard_frame, bg=self.colors['background'])
        stats_container.pack(fill=tk.X)

        # تكوين الأعمدة
        for i in range(4):
            stats_container.columnconfigure(i, weight=1)

        # بيانات الإحصائيات
        stats_data = [
            {
                'title': 'عينات اليوم',
                'value': str(self.get_today_samples_count()),
                'icon': '🧪',
                'color': self.colors['secondary'],
                'change': '+12%'
            },
            {
                'title': 'نتائج معلقة',
                'value': str(self.get_pending_results_count()),
                'icon': '⏳',
                'color': self.colors['warning'],
                'change': '-5%'
            },
            {
                'title': 'إجمالي العينات',
                'value': str(self.get_total_samples_count()),
                'icon': '📊',
                'color': self.colors['success'],
                'change': '+8%'
            },
            {
                'title': 'معدل الإنجاز',
                'value': f"{self.get_completion_rate():.1f}%",
                'icon': '✅',
                'color': self.colors['primary'],
                'change': '+3%'
            }
        ]

        # إنشاء بطاقات الإحصائيات
        for i, stat_data in enumerate(stats_data):
            self.create_stat_card(stats_container, stat_data, i)

        # رسم بياني سريع
        self.setup_quick_chart(dashboard_frame)

    def create_stat_card(self, parent, stat_data, column):
        """إنشاء بطاقة إحصائية"""
        # إطار البطاقة
        card_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=0)
        card_frame.grid(row=0, column=column, padx=10, pady=10, sticky='ew')

        # محتوى البطاقة
        content_frame = tk.Frame(card_frame, bg=self.colors['card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # الصف العلوي (الأيقونة والتغيير)
        top_frame = tk.Frame(content_frame, bg=self.colors['card'])
        top_frame.pack(fill=tk.X, pady=(0, 10))

        # الأيقونة
        icon_label = tk.Label(top_frame,
                             text=stat_data['icon'],
                             font=('Segoe UI Emoji', 24),
                             bg=self.colors['card'],
                             fg=stat_data['color'])
        icon_label.pack(side=tk.LEFT)

        # نسبة التغيير
        change_color = self.colors['success'] if stat_data['change'].startswith('+') else self.colors['danger']
        change_label = tk.Label(top_frame,
                               text=stat_data['change'],
                               font=self.fonts['small'],
                               bg=self.colors['card'],
                               fg=change_color)
        change_label.pack(side=tk.RIGHT)

        # القيمة الرئيسية
        value_label = tk.Label(content_frame,
                              text=stat_data['value'],
                              font=('Segoe UI', 28, 'bold'),
                              bg=self.colors['card'],
                              fg=stat_data['color'])
        value_label.pack(anchor=tk.W, pady=(0, 5))

        # العنوان
        title_label = tk.Label(content_frame,
                              text=stat_data['title'],
                              font=self.fonts['body'],
                              bg=self.colors['card'],
                              fg=self.colors['text_light'])
        title_label.pack(anchor=tk.W)

    def setup_quick_chart(self, parent):
        """إعداد رسم بياني سريع"""
        # إطار الرسم البياني
        chart_frame = tk.Frame(parent, bg=self.colors['card'])
        chart_frame.pack(fill=tk.X, pady=(20, 0))

        # عنوان الرسم البياني
        chart_title = tk.Label(chart_frame,
                              text="📈 العينات الأسبوعية",
                              font=self.fonts['subheading'],
                              bg=self.colors['card'],
                              fg=self.colors['text'])
        chart_title.pack(pady=20)

        # رسم بياني مبسط (نص فقط للآن)
        chart_content = tk.Label(chart_frame,
                                text="▁▃▅▇▆▄▂▅▇▆▄▂▁▃▅",
                                font=('Courier', 20),
                                bg=self.colors['card'],
                                fg=self.colors['secondary'])
        chart_content.pack(pady=(0, 20))

    def setup_modern_status_bar(self):
        """إعداد شريط الحالة العصري"""
        # إطار شريط الحالة
        status_frame = tk.Frame(self.root, bg=self.colors['primary'], height=40)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        status_frame.pack_propagate(False)

        # محتوى شريط الحالة
        content_frame = tk.Frame(status_frame, bg=self.colors['primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # النص الأساسي
        self.status_label = tk.Label(content_frame,
                                    text="🟢 جاهز",
                                    font=self.fonts['body'],
                                    bg=self.colors['primary'],
                                    fg='white')
        self.status_label.pack(side=tk.LEFT)

        # معلومات إضافية
        info_frame = tk.Frame(content_frame, bg=self.colors['primary'])
        info_frame.pack(side=tk.RIGHT)

        # المستخدم
        user_label = tk.Label(info_frame,
                             text="👤 مدير النظام",
                             font=self.fonts['small'],
                             bg=self.colors['primary'],
                             fg='#bdc3c7')
        user_label.pack(side=tk.RIGHT, padx=(20, 0))

        # الإصدار
        version_label = tk.Label(info_frame,
                                text="v1.0",
                                font=self.fonts['small'],
                                bg=self.colors['primary'],
                                fg='#bdc3c7')
        version_label.pack(side=tk.RIGHT, padx=(20, 0))

    def get_completion_rate(self) -> float:
        """حساب معدل الإنجاز"""
        try:
            total_tests = self.db_manager.execute_query("SELECT COUNT(*) as count FROM patient_tests")[0]['count']
            completed_tests = self.db_manager.execute_query("SELECT COUNT(*) as count FROM results")[0]['count']

            if total_tests > 0:
                return (completed_tests / total_tests) * 100
            return 0
        except:
            return 0
    
    def setup_quick_info(self, parent):
        """إعداد منطقة المعلومات السريعة"""
        # إحصائيات سريعة
        stats_frame = ttk.Frame(parent)
        stats_frame.pack(fill=tk.BOTH, expand=True)
        
        # عدد العينات اليوم
        today_samples = self.get_today_samples_count()
        ttk.Label(stats_frame, text=f"عينات اليوم: {today_samples}", 
                 font=('Arial', 14)).pack(anchor=tk.W, pady=5)
        
        # عدد النتائج المعلقة
        pending_results = self.get_pending_results_count()
        ttk.Label(stats_frame, text=f"نتائج معلقة: {pending_results}", 
                 font=('Arial', 14)).pack(anchor=tk.W, pady=5)
        
        # إجمالي العينات
        total_samples = self.get_total_samples_count()
        ttk.Label(stats_frame, text=f"إجمالي العينات: {total_samples}", 
                 font=('Arial', 14)).pack(anchor=tk.W, pady=5)
    
    def get_today_samples_count(self) -> int:
        """الحصول على عدد العينات اليوم"""
        today = datetime.now().strftime('%Y-%m-%d')
        result = self.db_manager.execute_query(
            "SELECT COUNT(*) as count FROM patients WHERE DATE(sample_received_date) = ?", 
            (today,)
        )
        return result[0]['count'] if result else 0
    
    def get_pending_results_count(self) -> int:
        """الحصول على عدد النتائج المعلقة"""
        result = self.db_manager.execute_query("""
            SELECT COUNT(*) as count FROM patient_tests pt
            LEFT JOIN results r ON pt.patient_id = r.patient_id AND pt.test_id = r.test_id
            WHERE r.id IS NULL
        """)
        return result[0]['count'] if result else 0
    
    def get_total_samples_count(self) -> int:
        """الحصول على إجمالي العينات"""
        result = self.db_manager.execute_query("SELECT COUNT(*) as count FROM patients")
        return result[0]['count'] if result else 0
    
    # دوال فتح النوافذ
    def open_data_entry(self):
        """فتح نافذة إدخال البيانات"""
        if self.data_entry_window is None or not self.data_entry_window.window.winfo_exists():
            self.data_entry_window = DataEntryWindow(self.root, self.db_manager, self)
        else:
            self.data_entry_window.window.lift()
    
    def open_work_window(self):
        """فتح نافذة العمل"""
        if self.work_window is None or not self.work_window.window.winfo_exists():
            self.work_window = WorkWindow(self.root, self.db_manager, self)
        else:
            self.work_window.window.lift()
    
    def open_results_window(self):
        """فتح نافذة النتائج"""
        if self.results_window is None or not self.results_window.window.winfo_exists():
            self.results_window = ResultsWindow(self.root, self.db_manager, self)
        else:
            self.results_window.window.lift()
    
    def open_reports_window(self):
        """فتح نافذة التقارير"""
        if self.reports_window is None or not self.reports_window.window.winfo_exists():
            self.reports_window = ReportsWindow(self.root, self.db_manager, self)
        else:
            self.reports_window.window.lift()
    
    def open_settings_window(self):
        """فتح نافذة الإعدادات"""
        if self.settings_window is None or not self.settings_window.window.winfo_exists():
            self.settings_window = SettingsWindow(self.root, self.db_manager, self)
        else:
            self.settings_window.window.lift()
    
    # دوال القوائم
    def new_file(self):
        """ملف جديد"""
        messagebox.showinfo("ملف جديد", "سيتم إضافة هذه الميزة قريباً")
    
    def open_file(self):
        """فتح ملف"""
        messagebox.showinfo("فتح ملف", "سيتم إضافة هذه الميزة قريباً")
    
    def save_file(self):
        """حفظ ملف"""
        messagebox.showinfo("حفظ ملف", "سيتم إضافة هذه الميزة قريباً")
    
    def exit_application(self):
        """خروج من التطبيق"""
        if messagebox.askokcancel("خروج", "هل تريد الخروج من التطبيق؟"):
            self.root.quit()
    
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        messagebox.showinfo("حول البرنامج", 
                           "مختبر الصحة العامة المركزي - ذي قار\n"
                           "الإصدار 1.0\n"
                           "وزارة الصحة العراقية\n"
                           "تم التطوير بواسطة فريق التطوير")
    
    def show_user_guide(self):
        """عرض دليل المستخدم"""
        messagebox.showinfo("دليل المستخدم", "سيتم إضافة دليل المستخدم قريباً")
    
    # دوال التزامن بين النوافذ
    def add_observer(self, observer):
        """إضافة مراقب للتحديثات"""
        self.observers.append(observer)
    
    def notify_observers(self, event_type: str, data: dict = None):
        """إشعار جميع المراقبين بالتحديثات"""
        for observer in self.observers:
            if hasattr(observer, 'on_data_changed'):
                observer.on_data_changed(event_type, data)
    
    def update_status(self, message: str):
        """تحديث شريط الحالة العصري"""
        # إضافة أيقونة حسب نوع الرسالة
        if "نجح" in message or "تم" in message:
            icon = "✅"
            color = "#2ecc71"
        elif "خطأ" in message or "فشل" in message:
            icon = "❌"
            color = "#e74c3c"
        elif "تحذير" in message:
            icon = "⚠️"
            color = "#f39c12"
        else:
            icon = "ℹ️"
            color = "white"

        self.status_label.config(text=f"{icon} {message}", fg=color)
        self.root.update_idletasks()

        # إعادة تعيين اللون بعد 3 ثوان
        self.root.after(3000, lambda: self.status_label.config(fg='white'))
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LabMainApplication()
    app.run()
