# -*- coding: utf-8 -*-
"""
مختبر الصحة العامة المركزي ذي قار
Central Public Health Laboratory - Dhi Qar
التطبيق الرئيسي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from data_entry_window import DataEntryWindow
from work_window import WorkWindow
from results_window import ResultsWindow
from reports_window import ReportsWindow
from settings_window import SettingsWindow

class LabMainApplication:
    def __init__(self):
        """تهيئة التطبيق الرئيسي"""
        self.root = tk.Tk()
        self.setup_main_window()
        self.db_manager = DatabaseManager()
        self.setup_menu()
        self.setup_main_interface()
        
        # النوافذ الفرعية
        self.data_entry_window = None
        self.work_window = None
        self.results_window = None
        self.reports_window = None
        self.settings_window = None
        
        # متغيرات للتزامن بين النوافذ
        self.observers = []
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("مختبر الصحة العامة المركزي - ذي قار")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # فتح النافذة بحجم كامل
        
        # تعيين الخط العربي
        self.root.option_add('*Font', 'Arial 12')
        
        # تعيين أيقونة التطبيق (إذا توفرت)
        try:
            self.root.iconbitmap('lab_icon.ico')
        except:
            pass
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_file)
        file_menu.add_command(label="فتح", command=self.open_file)
        file_menu.add_command(label="حفظ", command=self.save_file)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_application)
        
        # قائمة النوافذ
        windows_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="النوافذ", menu=windows_menu)
        windows_menu.add_command(label="إدخال البيانات", command=self.open_data_entry)
        windows_menu.add_command(label="العمل", command=self.open_work_window)
        windows_menu.add_command(label="النتائج", command=self.open_results_window)
        windows_menu.add_command(label="التقارير والإحصائيات", command=self.open_reports_window)
        windows_menu.add_command(label="الإعدادات", command=self.open_settings_window)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        help_menu.add_command(label="دليل المستخدم", command=self.show_user_guide)
    
    def setup_main_interface(self):
        """إعداد الواجهة الرئيسية"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # شعار وزارة الصحة
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, columnspan=2, pady=(0, 20), sticky=(tk.W, tk.E))
        
        title_label = ttk.Label(header_frame, text="مختبر الصحة العامة المركزي - ذي قار", 
                               font=('Arial', 24, 'bold'))
        title_label.pack()
        
        subtitle_label = ttk.Label(header_frame, text="وزارة الصحة العراقية", 
                                  font=('Arial', 16))
        subtitle_label.pack()
        
        # أزرار النوافذ الرئيسية
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=1, column=0, columnspan=2, pady=20, sticky=(tk.W, tk.E))
        
        # تكوين الأعمدة للأزرار
        for i in range(5):
            buttons_frame.columnconfigure(i, weight=1)
        
        # أزرار النوافذ
        ttk.Button(buttons_frame, text="إدخال البيانات", 
                  command=self.open_data_entry, width=20).grid(row=0, column=0, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="العمل", 
                  command=self.open_work_window, width=20).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="النتائج", 
                  command=self.open_results_window, width=20).grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="التقارير والإحصائيات", 
                  command=self.open_reports_window, width=20).grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="الإعدادات", 
                  command=self.open_settings_window, width=20).grid(row=0, column=4, padx=5, pady=5)
        
        # منطقة المعلومات والإحصائيات السريعة
        info_frame = ttk.LabelFrame(main_frame, text="معلومات سريعة", padding="10")
        info_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=20)
        
        self.setup_quick_info(info_frame)
        
        # شريط الحالة
        self.status_bar = ttk.Label(self.root, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))
    
    def setup_quick_info(self, parent):
        """إعداد منطقة المعلومات السريعة"""
        # إحصائيات سريعة
        stats_frame = ttk.Frame(parent)
        stats_frame.pack(fill=tk.BOTH, expand=True)
        
        # عدد العينات اليوم
        today_samples = self.get_today_samples_count()
        ttk.Label(stats_frame, text=f"عينات اليوم: {today_samples}", 
                 font=('Arial', 14)).pack(anchor=tk.W, pady=5)
        
        # عدد النتائج المعلقة
        pending_results = self.get_pending_results_count()
        ttk.Label(stats_frame, text=f"نتائج معلقة: {pending_results}", 
                 font=('Arial', 14)).pack(anchor=tk.W, pady=5)
        
        # إجمالي العينات
        total_samples = self.get_total_samples_count()
        ttk.Label(stats_frame, text=f"إجمالي العينات: {total_samples}", 
                 font=('Arial', 14)).pack(anchor=tk.W, pady=5)
    
    def get_today_samples_count(self) -> int:
        """الحصول على عدد العينات اليوم"""
        today = datetime.now().strftime('%Y-%m-%d')
        result = self.db_manager.execute_query(
            "SELECT COUNT(*) as count FROM patients WHERE DATE(sample_received_date) = ?", 
            (today,)
        )
        return result[0]['count'] if result else 0
    
    def get_pending_results_count(self) -> int:
        """الحصول على عدد النتائج المعلقة"""
        result = self.db_manager.execute_query("""
            SELECT COUNT(*) as count FROM patient_tests pt
            LEFT JOIN results r ON pt.patient_id = r.patient_id AND pt.test_id = r.test_id
            WHERE r.id IS NULL
        """)
        return result[0]['count'] if result else 0
    
    def get_total_samples_count(self) -> int:
        """الحصول على إجمالي العينات"""
        result = self.db_manager.execute_query("SELECT COUNT(*) as count FROM patients")
        return result[0]['count'] if result else 0
    
    # دوال فتح النوافذ
    def open_data_entry(self):
        """فتح نافذة إدخال البيانات"""
        if self.data_entry_window is None or not self.data_entry_window.window.winfo_exists():
            self.data_entry_window = DataEntryWindow(self.root, self.db_manager, self)
        else:
            self.data_entry_window.window.lift()
    
    def open_work_window(self):
        """فتح نافذة العمل"""
        if self.work_window is None or not self.work_window.window.winfo_exists():
            self.work_window = WorkWindow(self.root, self.db_manager, self)
        else:
            self.work_window.window.lift()
    
    def open_results_window(self):
        """فتح نافذة النتائج"""
        if self.results_window is None or not self.results_window.window.winfo_exists():
            self.results_window = ResultsWindow(self.root, self.db_manager, self)
        else:
            self.results_window.window.lift()
    
    def open_reports_window(self):
        """فتح نافذة التقارير"""
        if self.reports_window is None or not self.reports_window.window.winfo_exists():
            self.reports_window = ReportsWindow(self.root, self.db_manager, self)
        else:
            self.reports_window.window.lift()
    
    def open_settings_window(self):
        """فتح نافذة الإعدادات"""
        if self.settings_window is None or not self.settings_window.window.winfo_exists():
            self.settings_window = SettingsWindow(self.root, self.db_manager, self)
        else:
            self.settings_window.window.lift()
    
    # دوال القوائم
    def new_file(self):
        """ملف جديد"""
        messagebox.showinfo("ملف جديد", "سيتم إضافة هذه الميزة قريباً")
    
    def open_file(self):
        """فتح ملف"""
        messagebox.showinfo("فتح ملف", "سيتم إضافة هذه الميزة قريباً")
    
    def save_file(self):
        """حفظ ملف"""
        messagebox.showinfo("حفظ ملف", "سيتم إضافة هذه الميزة قريباً")
    
    def exit_application(self):
        """خروج من التطبيق"""
        if messagebox.askokcancel("خروج", "هل تريد الخروج من التطبيق؟"):
            self.root.quit()
    
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        messagebox.showinfo("حول البرنامج", 
                           "مختبر الصحة العامة المركزي - ذي قار\n"
                           "الإصدار 1.0\n"
                           "وزارة الصحة العراقية\n"
                           "تم التطوير بواسطة فريق التطوير")
    
    def show_user_guide(self):
        """عرض دليل المستخدم"""
        messagebox.showinfo("دليل المستخدم", "سيتم إضافة دليل المستخدم قريباً")
    
    # دوال التزامن بين النوافذ
    def add_observer(self, observer):
        """إضافة مراقب للتحديثات"""
        self.observers.append(observer)
    
    def notify_observers(self, event_type: str, data: dict = None):
        """إشعار جميع المراقبين بالتحديثات"""
        for observer in self.observers:
            if hasattr(observer, 'on_data_changed'):
                observer.on_data_changed(event_type, data)
    
    def update_status(self, message: str):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=message)
        self.root.update_idletasks()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LabMainApplication()
    app.run()
