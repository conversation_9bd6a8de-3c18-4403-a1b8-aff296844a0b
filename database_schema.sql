-- قاعدة بيانات مختبر الصحة العامة المركزي ذي قار
-- Central Public Health Laboratory Database Schema

-- جدول أنواع العينات
CREATE TABLE sample_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول جهات الإرسال
CREATE TABLE sending_entities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول التحاليل
CREATE TABLE tests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المرضى والعينات (النافذة الأولى)
CREATE TABLE patients (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    national_number INTEGER NOT NULL UNIQUE, -- الرقم الوطني (تلقائي)
    name TEXT NOT NULL, -- الاسم (إلزامي)
    age INTEGER NOT NULL, -- العمر (إلزامي)
    gender TEXT NOT NULL CHECK (gender IN ('M', 'F')), -- الجنس (إلزامي)
    address TEXT NOT NULL, -- العنوان (إلزامي)
    phone TEXT NOT NULL, -- رقم الهاتف (إلزامي)
    passport_number TEXT, -- رقم الجواز (اختياري)
    receipt_number TEXT, -- رقم الوصل (اختياري)
    sample_type_id INTEGER NOT NULL, -- نوع العينة (إلزامي)
    sending_entity_id INTEGER NOT NULL, -- جهة الإرسال (إلزامي)
    sample_collection_date DATE NOT NULL, -- تاريخ سحب العينة (إلزامي)
    sample_received_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- تاريخ استلام العينة (تلقائي)
    barcode TEXT NOT NULL UNIQUE, -- الباركود (تلقائي)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sample_type_id) REFERENCES sample_types(id),
    FOREIGN KEY (sending_entity_id) REFERENCES sending_entities(id)
);

-- جدول تحاليل المرضى (علاقة many-to-many)
CREATE TABLE patient_tests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id INTEGER NOT NULL,
    test_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (test_id) REFERENCES tests(id),
    UNIQUE(patient_id, test_id)
);

-- جدول الوجبات (النافذة الثانية)
CREATE TABLE batches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_number INTEGER NOT NULL UNIQUE, -- رقم الوجبة (تلقائي)
    start_date DATE NOT NULL, -- تاريخ البداية للفترة
    end_date DATE NOT NULL, -- تاريخ النهاية للفترة
    work_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- تاريخ العمل (تلقائي)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عينات الوجبة
CREATE TABLE batch_samples (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id INTEGER NOT NULL,
    patient_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE CASCADE,
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    UNIQUE(batch_id, patient_id)
);

-- جدول العاملين في التحاليل
CREATE TABLE batch_workers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id INTEGER NOT NULL,
    test_id INTEGER NOT NULL,
    worker_name TEXT NOT NULL, -- اسم العامل
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE CASCADE,
    FOREIGN KEY (test_id) REFERENCES tests(id)
);

-- جدول النتائج (النافذة الثالثة)
CREATE TABLE results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id INTEGER NOT NULL,
    test_id INTEGER NOT NULL,
    result TEXT NOT NULL CHECK (result IN ('Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND')),
    result_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (test_id) REFERENCES tests(id),
    UNIQUE(patient_id, test_id)
);

-- إدراج بيانات أولية
INSERT INTO sample_types (name) VALUES 
('دم'), ('بول'), ('براز'), ('مسحة'), ('بلغم'), ('سائل شوكي');

INSERT INTO sending_entities (name) VALUES 
('مستشفى الناصرية العام'), ('مستشفى الحبوبي'), ('مستشفى الشطرة'), 
('مركز صحي المدينة'), ('مركز صحي الشهداء'), ('عيادة خاصة');

INSERT INTO tests (name, description) VALUES 
('فحص كوفيد-19', 'فحص فيروس كورونا'),
('فحص الملاريا', 'فحص طفيلي الملاريا'),
('فحص السكري', 'فحص مستوى السكر في الدم'),
('فحص الكوليسترول', 'فحص مستوى الكوليسترول'),
('تحليل البول الكامل', 'فحص شامل للبول'),
('تحليل الدم الكامل', 'فحص شامل للدم');
