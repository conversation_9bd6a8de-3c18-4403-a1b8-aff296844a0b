@echo off
echo ========================================
echo   تثبيت متطلبات النظام
echo   Installing System Requirements
echo ========================================
echo.

echo جاري تثبيت المتطلبات...
echo Installing requirements...
echo.

pip install python-barcode Pillow reportlab openpyxl pandas ttkthemes opencv-python pyzbar matplotlib

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo تم تثبيت جميع المتطلبات بنجاح!
    echo All requirements installed successfully!
    echo ========================================
    echo.
    echo يمكنك الآن تشغيل النظام باستخدام run_lab.bat
    echo You can now run the system using run_lab.bat
    echo.
) else (
    echo.
    echo ========================================
    echo خطأ في تثبيت المتطلبات!
    echo Error installing requirements!
    echo ========================================
    echo.
    echo يرجى التأكد من:
    echo Please check:
    echo - اتصال الإنترنت
    echo - تثبيت Python بشكل صحيح
    echo - صلاحيات المدير
    echo.
)

pause
