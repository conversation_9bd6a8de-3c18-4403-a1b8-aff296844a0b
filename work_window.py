# -*- coding: utf-8 -*-
"""
نافذة العمل - مختبر الصحة العامة المركزي ذي قار
Work Window - Central Public Health Laboratory
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date, timedelta
from typing import Dict, List, Any
import calendar

class WorkWindow:
    def __init__(self, parent, db_manager, main_app):
        """تهيئة نافذة العمل"""
        self.parent = parent
        self.db_manager = db_manager
        self.main_app = main_app
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_variables()
        self.setup_interface()
        self.load_data()
        
        # تسجيل كمراقب للتحديثات
        self.main_app.add_observer(self)
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("العمل - مختبر الصحة العامة المركزي")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
    
    def setup_variables(self):
        """إعداد المتغيرات"""
        # متغيرات الفترة الزمنية
        self.start_date_var = tk.StringVar()
        self.end_date_var = tk.StringVar()
        
        # متغيرات الوجبة
        self.batch_number_var = tk.StringVar()
        self.work_date_var = tk.StringVar()
        
        # قوائم البيانات
        self.available_samples = []
        self.selected_samples = []
        self.tests = []
        self.current_batch_id = None
        
        # تعيين التواريخ الافتراضية
        today = datetime.now()
        self.start_date_var.set(today.strftime('%Y-%m-%d'))
        self.end_date_var.set(today.strftime('%Y-%m-%d'))
        self.work_date_var.set(today.strftime('%Y-%m-%d'))
    
    def setup_interface(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إطار اختيار الفترة
        period_frame = ttk.LabelFrame(main_frame, text="اختيار فترة العينات", padding="10")
        period_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.setup_period_selection(period_frame)
        
        # إطار الوجبة
        batch_frame = ttk.LabelFrame(main_frame, text="معلومات الوجبة", padding="10")
        batch_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.setup_batch_info(batch_frame)
        
        # إطار العينات
        samples_frame = ttk.LabelFrame(main_frame, text="العينات", padding="10")
        samples_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.setup_samples_section(samples_frame)
        
        # إطار العاملين
        workers_frame = ttk.LabelFrame(main_frame, text="العاملين في التحاليل", padding="10")
        workers_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.setup_workers_section(workers_frame)
        
        # إطار العمليات
        operations_frame = ttk.LabelFrame(main_frame, text="العمليات", padding="10")
        operations_frame.pack(fill=tk.X)
        
        self.setup_operations_section(operations_frame)
    
    def setup_period_selection(self, parent):
        """إعداد قسم اختيار الفترة"""
        # الصف الأول - اختيار التواريخ
        date_frame = ttk.Frame(parent)
        date_frame.pack(fill=tk.X, pady=(0, 10))
        
        # تاريخ البداية
        ttk.Label(date_frame, text="من تاريخ:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        start_date_entry = ttk.Entry(date_frame, textvariable=self.start_date_var, width=12)
        start_date_entry.grid(row=0, column=1, padx=(0, 20))
        
        # تاريخ النهاية
        ttk.Label(date_frame, text="إلى تاريخ:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        end_date_entry = ttk.Entry(date_frame, textvariable=self.end_date_var, width=12)
        end_date_entry.grid(row=0, column=3, padx=(0, 20))
        
        # أزرار سريعة للتواريخ
        ttk.Button(date_frame, text="اليوم", 
                  command=self.set_today).grid(row=0, column=4, padx=5)
        ttk.Button(date_frame, text="أمس", 
                  command=self.set_yesterday).grid(row=0, column=5, padx=5)
        ttk.Button(date_frame, text="هذا الأسبوع", 
                  command=self.set_this_week).grid(row=0, column=6, padx=5)
        
        # زر تحميل العينات
        ttk.Button(date_frame, text="تحميل العينات", 
                  command=self.load_samples_for_period, 
                  style='Accent.TButton').grid(row=0, column=7, padx=(20, 0))
        
        # معلومات سريعة
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X)
        
        self.samples_count_label = ttk.Label(info_frame, text="عدد العينات: 0")
        self.samples_count_label.pack(side=tk.LEFT, padx=(0, 20))
        
        self.selected_count_label = ttk.Label(info_frame, text="العينات المحددة: 0")
        self.selected_count_label.pack(side=tk.LEFT)
    
    def setup_batch_info(self, parent):
        """إعداد معلومات الوجبة"""
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X)
        
        # رقم الوجبة
        ttk.Label(info_frame, text="رقم الوجبة:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        batch_entry = ttk.Entry(info_frame, textvariable=self.batch_number_var, 
                               state='readonly', width=10)
        batch_entry.grid(row=0, column=1, padx=(0, 20))
        
        # تاريخ العمل
        ttk.Label(info_frame, text="تاريخ العمل:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        work_date_entry = ttk.Entry(info_frame, textvariable=self.work_date_var, width=12)
        work_date_entry.grid(row=0, column=3, padx=(0, 20))
        
        # زر إنشاء وجبة جديدة
        ttk.Button(info_frame, text="إنشاء وجبة جديدة", 
                  command=self.create_new_batch).grid(row=0, column=4, padx=10)
        
        # زر حفظ الوجبة
        ttk.Button(info_frame, text="حفظ الوجبة", 
                  command=self.save_batch).grid(row=0, column=5, padx=5)
    
    def setup_samples_section(self, parent):
        """إعداد قسم العينات"""
        # إطار العينات المتاحة والمحددة
        samples_paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        samples_paned.pack(fill=tk.BOTH, expand=True)
        
        # العينات المتاحة
        available_frame = ttk.LabelFrame(samples_paned, text="العينات المتاحة", padding="5")
        samples_paned.add(available_frame, weight=1)
        
        # جدول العينات المتاحة
        available_columns = ('الرقم الوطني', 'الاسم', 'نوع العينة', 'تاريخ السحب')
        self.available_tree = ttk.Treeview(available_frame, columns=available_columns, 
                                          show='headings', height=15)
        
        for col in available_columns:
            self.available_tree.heading(col, text=col)
            self.available_tree.column(col, width=100, anchor=tk.CENTER)
        
        # شريط التمرير للعينات المتاحة
        available_scrollbar = ttk.Scrollbar(available_frame, orient=tk.VERTICAL, 
                                           command=self.available_tree.yview)
        self.available_tree.config(yscrollcommand=available_scrollbar.set)
        
        self.available_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        available_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار التحكم
        control_frame = ttk.Frame(samples_paned)
        samples_paned.add(control_frame, weight=0)
        
        control_buttons = ttk.Frame(control_frame)
        control_buttons.pack(expand=True)
        
        ttk.Button(control_buttons, text="إضافة >>", 
                  command=self.add_selected_samples).pack(pady=5, fill=tk.X)
        ttk.Button(control_buttons, text="إضافة الكل >>>", 
                  command=self.add_all_samples).pack(pady=5, fill=tk.X)
        ttk.Button(control_buttons, text="<< إزالة", 
                  command=self.remove_selected_samples).pack(pady=5, fill=tk.X)
        ttk.Button(control_buttons, text="<<< إزالة الكل", 
                  command=self.remove_all_samples).pack(pady=5, fill=tk.X)
        
        # العينات المحددة للوجبة
        selected_frame = ttk.LabelFrame(samples_paned, text="عينات الوجبة", padding="5")
        samples_paned.add(selected_frame, weight=1)
        
        # جدول العينات المحددة
        selected_columns = ('الرقم الوطني', 'الاسم', 'نوع العينة', 'التحاليل')
        self.selected_tree = ttk.Treeview(selected_frame, columns=selected_columns, 
                                         show='headings', height=15)
        
        for col in selected_columns:
            self.selected_tree.heading(col, text=col)
            self.selected_tree.column(col, width=100, anchor=tk.CENTER)
        
        # شريط التمرير للعينات المحددة
        selected_scrollbar = ttk.Scrollbar(selected_frame, orient=tk.VERTICAL, 
                                          command=self.selected_tree.yview)
        self.selected_tree.config(yscrollcommand=selected_scrollbar.set)
        
        self.selected_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        selected_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.available_tree.bind('<Double-1>', self.on_available_double_click)
        self.selected_tree.bind('<Double-1>', self.on_selected_double_click)
    
    def setup_workers_section(self, parent):
        """إعداد قسم العاملين"""
        # إطار التحاليل والعاملين
        workers_frame = ttk.Frame(parent)
        workers_frame.pack(fill=tk.X)
        
        # جدول التحاليل والعاملين
        workers_columns = ('التحليل', 'اسم العامل', 'عدد العينات')
        self.workers_tree = ttk.Treeview(workers_frame, columns=workers_columns, 
                                        show='headings', height=6)
        
        for col in workers_columns:
            self.workers_tree.heading(col, text=col)
            self.workers_tree.column(col, width=150, anchor=tk.CENTER)
        
        # شريط التمرير
        workers_scrollbar = ttk.Scrollbar(workers_frame, orient=tk.VERTICAL, 
                                         command=self.workers_tree.yview)
        self.workers_tree.config(yscrollcommand=workers_scrollbar.set)
        
        self.workers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        workers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار إدارة العاملين
        workers_buttons = ttk.Frame(parent)
        workers_buttons.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(workers_buttons, text="إضافة عامل", 
                  command=self.add_worker).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(workers_buttons, text="تعديل عامل", 
                  command=self.edit_worker).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(workers_buttons, text="حذف عامل", 
                  command=self.delete_worker).pack(side=tk.LEFT)
        
        # ربط الأحداث
        self.workers_tree.bind('<Double-1>', self.on_worker_double_click)
    
    def setup_operations_section(self, parent):
        """إعداد قسم العمليات"""
        operations_frame = ttk.Frame(parent)
        operations_frame.pack(fill=tk.X)
        
        # أزرار العمليات الرئيسية
        ttk.Button(operations_frame, text="حفظ الوجبة", 
                  command=self.save_batch, 
                  style='Accent.TButton').pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(operations_frame, text="حذف الوجبة", 
                  command=self.delete_batch).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(operations_frame, text="طباعة قائمة العمل", 
                  command=self.print_work_list).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(operations_frame, text="تحديث", 
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(operations_frame, text="مسح",
                  command=self.clear_batch).pack(side=tk.LEFT)

    def load_data(self):
        """تحميل البيانات الأولية"""
        # تحميل التحاليل
        self.tests = self.db_manager.get_tests()

        # تعيين رقم الوجبة التالي
        next_batch = self.db_manager.get_next_batch_number()
        self.batch_number_var.set(str(next_batch))

        # تحميل العينات للفترة الحالية
        self.load_samples_for_period()

    # دوال التواريخ السريعة
    def set_today(self):
        """تعيين تاريخ اليوم"""
        today = datetime.now().strftime('%Y-%m-%d')
        self.start_date_var.set(today)
        self.end_date_var.set(today)

    def set_yesterday(self):
        """تعيين تاريخ أمس"""
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        self.start_date_var.set(yesterday)
        self.end_date_var.set(yesterday)

    def set_this_week(self):
        """تعيين هذا الأسبوع"""
        today = datetime.now()
        start_of_week = today - timedelta(days=today.weekday())
        end_of_week = start_of_week + timedelta(days=6)

        self.start_date_var.set(start_of_week.strftime('%Y-%m-%d'))
        self.end_date_var.set(end_of_week.strftime('%Y-%m-%d'))

    def load_samples_for_period(self):
        """تحميل العينات للفترة المحددة"""
        try:
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()

            # التحقق من صحة التواريخ
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')

            # مسح القائمة الحالية
            for item in self.available_tree.get_children():
                self.available_tree.delete(item)

            # تحميل العينات
            query = """
                SELECT p.id, p.national_number, p.name, st.name as sample_type_name,
                       p.sample_collection_date, p.sample_received_date
                FROM patients p
                JOIN sample_types st ON p.sample_type_id = st.id
                WHERE DATE(p.sample_received_date) BETWEEN ? AND ?
                AND p.id NOT IN (
                    SELECT DISTINCT patient_id FROM batch_samples
                )
                ORDER BY p.sample_received_date DESC
            """

            self.available_samples = self.db_manager.execute_query(query, (start_date, end_date))

            # عرض العينات في الجدول
            for sample in self.available_samples:
                self.available_tree.insert('', tk.END, values=(
                    sample['national_number'],
                    sample['name'],
                    sample['sample_type_name'],
                    sample['sample_collection_date']
                ))

            # تحديث عداد العينات
            self.samples_count_label.config(text=f"عدد العينات: {len(self.available_samples)}")

            self.main_app.update_status(f"تم تحميل {len(self.available_samples)} عينة للفترة من {start_date} إلى {end_date}")

        except ValueError:
            messagebox.showerror("خطأ", "صيغة التاريخ غير صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل العينات: {str(e)}")

    # دوال إدارة العينات
    def add_selected_samples(self):
        """إضافة العينات المحددة إلى الوجبة"""
        selected_items = self.available_tree.selection()

        if not selected_items:
            messagebox.showwarning("تحذير", "يرجى اختيار عينات لإضافتها")
            return

        for item in selected_items:
            values = self.available_tree.item(item)['values']
            national_number = values[0]

            # البحث عن العينة في القائمة
            sample_data = None
            for sample in self.available_samples:
                if sample['national_number'] == national_number:
                    sample_data = sample
                    break

            if sample_data:
                # الحصول على تحاليل العينة
                tests_query = """
                    SELECT t.name FROM patient_tests pt
                    JOIN tests t ON pt.test_id = t.id
                    WHERE pt.patient_id = ?
                """
                tests_result = self.db_manager.execute_query(tests_query, (sample_data['id'],))
                tests_names = [test['name'] for test in tests_result]

                # إضافة إلى قائمة العينات المحددة
                self.selected_tree.insert('', tk.END, values=(
                    values[0],  # الرقم الوطني
                    values[1],  # الاسم
                    values[2],  # نوع العينة
                    ', '.join(tests_names)  # التحاليل
                ))

                # إزالة من القائمة المتاحة
                self.available_tree.delete(item)
                self.available_samples.remove(sample_data)

        # تحديث العدادات
        self.update_counters()
        self.update_workers_list()

    def add_all_samples(self):
        """إضافة جميع العينات إلى الوجبة"""
        # تحديد جميع العينات
        all_items = self.available_tree.get_children()
        self.available_tree.selection_set(all_items)

        # إضافتها
        self.add_selected_samples()

    def remove_selected_samples(self):
        """إزالة العينات المحددة من الوجبة"""
        selected_items = self.selected_tree.selection()

        if not selected_items:
            messagebox.showwarning("تحذير", "يرجى اختيار عينات لإزالتها")
            return

        for item in selected_items:
            values = self.selected_tree.item(item)['values']
            national_number = values[0]

            # البحث عن العينة في قاعدة البيانات
            sample_query = """
                SELECT p.id, p.national_number, p.name, st.name as sample_type_name,
                       p.sample_collection_date
                FROM patients p
                JOIN sample_types st ON p.sample_type_id = st.id
                WHERE p.national_number = ?
            """
            sample_result = self.db_manager.execute_query(sample_query, (national_number,))

            if sample_result:
                sample_data = sample_result[0]

                # إضافة إلى القائمة المتاحة
                self.available_tree.insert('', tk.END, values=(
                    sample_data['national_number'],
                    sample_data['name'],
                    sample_data['sample_type_name'],
                    sample_data['sample_collection_date']
                ))

                self.available_samples.append(sample_data)

            # إزالة من القائمة المحددة
            self.selected_tree.delete(item)

        # تحديث العدادات
        self.update_counters()
        self.update_workers_list()

    def remove_all_samples(self):
        """إزالة جميع العينات من الوجبة"""
        # تحديد جميع العينات
        all_items = self.selected_tree.get_children()
        self.selected_tree.selection_set(all_items)

        # إزالتها
        self.remove_selected_samples()

    def update_counters(self):
        """تحديث العدادات"""
        available_count = len(self.available_tree.get_children())
        selected_count = len(self.selected_tree.get_children())

        self.samples_count_label.config(text=f"عدد العينات: {available_count}")
        self.selected_count_label.config(text=f"العينات المحددة: {selected_count}")

    def update_workers_list(self):
        """تحديث قائمة العاملين والتحاليل"""
        # مسح القائمة الحالية
        for item in self.workers_tree.get_children():
            self.workers_tree.delete(item)

        # حساب عدد العينات لكل تحليل
        test_counts = {}

        for item in self.selected_tree.get_children():
            values = self.selected_tree.item(item)['values']
            tests_str = values[3]  # التحاليل

            if tests_str:
                tests_list = [test.strip() for test in tests_str.split(',')]
                for test_name in tests_list:
                    if test_name in test_counts:
                        test_counts[test_name] += 1
                    else:
                        test_counts[test_name] = 1

        # عرض التحاليل في الجدول
        for test_name, count in test_counts.items():
            self.workers_tree.insert('', tk.END, values=(
                test_name,
                "",  # اسم العامل (فارغ في البداية)
                count
            ))

    # دوال إدارة العاملين
    def add_worker(self):
        """إضافة عامل لتحليل"""
        selected_items = self.workers_tree.selection()

        if not selected_items:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل لإضافة عامل له")
            return

        # نافذة إدخال اسم العامل
        worker_dialog = WorkerDialog(self.window, "إضافة عامل")
        if worker_dialog.result:
            worker_name = worker_dialog.result

            # تحديث اسم العامل في الجدول
            item = selected_items[0]
            values = list(self.workers_tree.item(item)['values'])
            values[1] = worker_name
            self.workers_tree.item(item, values=values)

            self.main_app.update_status(f"تم إضافة العامل: {worker_name}")

    def edit_worker(self):
        """تعديل عامل"""
        selected_items = self.workers_tree.selection()

        if not selected_items:
            messagebox.showwarning("تحذير", "يرجى اختيار عامل للتعديل")
            return

        item = selected_items[0]
        current_worker = self.workers_tree.item(item)['values'][1]

        # نافذة تعديل اسم العامل
        worker_dialog = WorkerDialog(self.window, "تعديل عامل", current_worker)
        if worker_dialog.result:
            worker_name = worker_dialog.result

            # تحديث اسم العامل في الجدول
            values = list(self.workers_tree.item(item)['values'])
            values[1] = worker_name
            self.workers_tree.item(item, values=values)

            self.main_app.update_status(f"تم تعديل العامل إلى: {worker_name}")

    def delete_worker(self):
        """حذف عامل"""
        selected_items = self.workers_tree.selection()

        if not selected_items:
            messagebox.showwarning("تحذير", "يرجى اختيار عامل للحذف")
            return

        if messagebox.askyesno("تأكيد", "هل تريد حذف العامل من هذا التحليل؟"):
            item = selected_items[0]
            values = list(self.workers_tree.item(item)['values'])
            values[1] = ""  # مسح اسم العامل
            self.workers_tree.item(item, values=values)

            self.main_app.update_status("تم حذف العامل")

    # دوال الوجبة
    def create_new_batch(self):
        """إنشاء وجبة جديدة"""
        if len(self.selected_tree.get_children()) > 0:
            if not messagebox.askyesno("تأكيد", "سيتم مسح العينات المحددة حالياً. هل تريد المتابعة؟"):
                return

        # مسح البيانات الحالية
        self.clear_batch()

        # تعيين رقم وجبة جديد
        next_batch = self.db_manager.get_next_batch_number()
        self.batch_number_var.set(str(next_batch))
        self.work_date_var.set(datetime.now().strftime('%Y-%m-%d'))

        self.current_batch_id = None
        self.main_app.update_status("تم إنشاء وجبة جديدة")

    def save_batch(self):
        """حفظ الوجبة"""
        # التحقق من وجود عينات
        if len(self.selected_tree.get_children()) == 0:
            messagebox.showwarning("تحذير", "يجب إضافة عينات إلى الوجبة قبل الحفظ")
            return

        try:
            # التحقق من صحة التاريخ
            work_date = self.work_date_var.get()
            datetime.strptime(work_date, '%Y-%m-%d')

            if self.current_batch_id:
                # تحديث وجبة موجودة
                self.update_existing_batch()
            else:
                # إنشاء وجبة جديدة
                self.create_new_batch_record()

        except ValueError:
            messagebox.showerror("خطأ", "صيغة تاريخ العمل غير صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الوجبة: {str(e)}")

    def create_new_batch_record(self):
        """إنشاء سجل وجبة جديد"""
        # إدراج الوجبة
        batch_query = """
            INSERT INTO batches (batch_number, start_date, end_date, work_date)
            VALUES (?, ?, ?, ?)
        """

        batch_params = (
            int(self.batch_number_var.get()),
            self.start_date_var.get(),
            self.end_date_var.get(),
            self.work_date_var.get()
        )

        if self.db_manager.execute_update(batch_query, batch_params):
            # الحصول على معرف الوجبة
            batch_result = self.db_manager.execute_query(
                "SELECT id FROM batches WHERE batch_number = ?",
                (int(self.batch_number_var.get()),)
            )

            if batch_result:
                self.current_batch_id = batch_result[0]['id']

                # إضافة العينات إلى الوجبة
                self.save_batch_samples()

                # إضافة العاملين
                self.save_batch_workers()

                messagebox.showinfo("نجح", "تم حفظ الوجبة بنجاح")

                # إشعار النوافذ الأخرى
                self.main_app.notify_observers('batch_created', {'batch_id': self.current_batch_id})
            else:
                messagebox.showerror("خطأ", "فشل في الحصول على معرف الوجبة")
        else:
            messagebox.showerror("خطأ", "فشل في حفظ الوجبة")

    def update_existing_batch(self):
        """تحديث وجبة موجودة"""
        # تحديث بيانات الوجبة
        batch_query = """
            UPDATE batches SET
                start_date = ?, end_date = ?, work_date = ?
            WHERE id = ?
        """

        batch_params = (
            self.start_date_var.get(),
            self.end_date_var.get(),
            self.work_date_var.get(),
            self.current_batch_id
        )

        if self.db_manager.execute_update(batch_query, batch_params):
            # حذف العينات والعاملين القدامى
            self.db_manager.execute_update("DELETE FROM batch_samples WHERE batch_id = ?", (self.current_batch_id,))
            self.db_manager.execute_update("DELETE FROM batch_workers WHERE batch_id = ?", (self.current_batch_id,))

            # إضافة العينات والعاملين الجدد
            self.save_batch_samples()
            self.save_batch_workers()

            messagebox.showinfo("نجح", "تم تحديث الوجبة بنجاح")

            # إشعار النوافذ الأخرى
            self.main_app.notify_observers('batch_updated', {'batch_id': self.current_batch_id})
        else:
            messagebox.showerror("خطأ", "فشل في تحديث الوجبة")

    def save_batch_samples(self):
        """حفظ عينات الوجبة"""
        for item in self.selected_tree.get_children():
            values = self.selected_tree.item(item)['values']
            national_number = values[0]

            # الحصول على معرف المريض
            patient_result = self.db_manager.execute_query(
                "SELECT id FROM patients WHERE national_number = ?",
                (national_number,)
            )

            if patient_result:
                patient_id = patient_result[0]['id']

                # إدراج العينة في الوجبة
                self.db_manager.execute_update(
                    "INSERT INTO batch_samples (batch_id, patient_id) VALUES (?, ?)",
                    (self.current_batch_id, patient_id)
                )

    def save_batch_workers(self):
        """حفظ عاملي الوجبة"""
        for item in self.workers_tree.get_children():
            values = self.workers_tree.item(item)['values']
            test_name = values[0]
            worker_name = values[1]

            if worker_name:  # فقط إذا كان هناك عامل محدد
                # الحصول على معرف التحليل
                test_result = self.db_manager.execute_query(
                    "SELECT id FROM tests WHERE name = ?",
                    (test_name,)
                )

                if test_result:
                    test_id = test_result[0]['id']

                    # إدراج العامل
                    self.db_manager.execute_update(
                        "INSERT INTO batch_workers (batch_id, test_id, worker_name) VALUES (?, ?, ?)",
                        (self.current_batch_id, test_id, worker_name)
                    )

    def delete_batch(self):
        """حذف الوجبة"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "لا توجد وجبة محفوظة للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه الوجبة؟"):
            if self.db_manager.execute_update("DELETE FROM batches WHERE id = ?", (self.current_batch_id,)):
                messagebox.showinfo("نجح", "تم حذف الوجبة بنجاح")
                self.clear_batch()

                # إشعار النوافذ الأخرى
                self.main_app.notify_observers('batch_deleted', {'batch_id': self.current_batch_id})
            else:
                messagebox.showerror("خطأ", "فشل في حذف الوجبة")

    def clear_batch(self):
        """مسح بيانات الوجبة"""
        # مسح العينات المحددة
        for item in self.selected_tree.get_children():
            self.selected_tree.delete(item)

        # مسح العاملين
        for item in self.workers_tree.get_children():
            self.workers_tree.delete(item)

        # إعادة تحميل العينات المتاحة
        self.load_samples_for_period()

        # تحديث العدادات
        self.update_counters()

        # إعادة تعيين معرف الوجبة
        self.current_batch_id = None

        self.main_app.update_status("تم مسح بيانات الوجبة")

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
        self.main_app.update_status("تم تحديث البيانات")

    # دوال الأحداث
    def on_available_double_click(self, event):
        """النقر المزدوج على عينة متاحة"""
        self.add_selected_samples()

    def on_selected_double_click(self, event):
        """النقر المزدوج على عينة محددة"""
        self.remove_selected_samples()

    def on_worker_double_click(self, event):
        """النقر المزدوج على عامل"""
        self.edit_worker()

    # دوال الطباعة
    def print_work_list(self):
        """طباعة قائمة العمل"""
        if len(self.selected_tree.get_children()) == 0:
            messagebox.showwarning("تحذير", "لا توجد عينات لطباعة قائمة العمل")
            return

        try:
            from work_list_printer import WorkListPrinter
            printer = WorkListPrinter()

            # جمع بيانات الوجبة
            batch_data = {
                'batch_number': self.batch_number_var.get(),
                'work_date': self.work_date_var.get(),
                'start_date': self.start_date_var.get(),
                'end_date': self.end_date_var.get(),
                'samples': [],
                'workers': []
            }

            # جمع بيانات العينات
            for item in self.selected_tree.get_children():
                values = self.selected_tree.item(item)['values']
                batch_data['samples'].append({
                    'national_number': values[0],
                    'name': values[1],
                    'sample_type': values[2],
                    'tests': values[3]
                })

            # جمع بيانات العاملين
            for item in self.workers_tree.get_children():
                values = self.workers_tree.item(item)['values']
                if values[1]:  # إذا كان هناك عامل محدد
                    batch_data['workers'].append({
                        'test': values[0],
                        'worker': values[1],
                        'count': values[2]
                    })

            # طباعة قائمة العمل
            printer.print_work_list(batch_data)

            self.main_app.update_status("تم إرسال قائمة العمل للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة قائمة العمل: {str(e)}")

    # دوال التزامن
    def on_data_changed(self, event_type: str, data: dict = None):
        """استقبال إشعارات التحديث من النوافذ الأخرى"""
        if event_type == 'patient_added':
            # إعادة تحميل العينات المتاحة
            self.load_samples_for_period()
        elif event_type == 'patient_deleted':
            # إزالة العينة من القوائم إذا كانت موجودة
            if data and 'patient_id' in data:
                self.remove_patient_from_lists(data['patient_id'])
        elif event_type == 'test_added' or event_type == 'test_updated' or event_type == 'test_deleted':
            # إعادة تحميل التحاليل
            self.tests = self.db_manager.get_tests()
            self.update_workers_list()

    def remove_patient_from_lists(self, patient_id: int):
        """إزالة مريض من القوائم"""
        # إزالة من العينات المتاحة
        for item in self.available_tree.get_children():
            values = self.available_tree.item(item)['values']
            national_number = values[0]

            # التحقق من معرف المريض
            patient_result = self.db_manager.execute_query(
                "SELECT id FROM patients WHERE national_number = ?",
                (national_number,)
            )

            if patient_result and patient_result[0]['id'] == patient_id:
                self.available_tree.delete(item)
                break

        # إزالة من العينات المحددة
        for item in self.selected_tree.get_children():
            values = self.selected_tree.item(item)['values']
            national_number = values[0]

            # التحقق من معرف المريض
            patient_result = self.db_manager.execute_query(
                "SELECT id FROM patients WHERE national_number = ?",
                (national_number,)
            )

            if patient_result and patient_result[0]['id'] == patient_id:
                self.selected_tree.delete(item)
                break

        # تحديث العدادات
        self.update_counters()
        self.update_workers_list()

    def __del__(self):
        """تنظيف الموارد عند إغلاق النافذة"""
        try:
            if hasattr(self, 'main_app') and self in self.main_app.observers:
                self.main_app.observers.remove(self)
        except:
            pass


class WorkerDialog:
    """نافذة حوار لإدخال اسم العامل"""

    def __init__(self, parent, title, initial_value=""):
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("300x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        # إعداد الواجهة
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # تسمية
        ttk.Label(main_frame, text="اسم العامل:").pack(anchor=tk.W, pady=(0, 10))

        # حقل الإدخال
        self.name_var = tk.StringVar(value=initial_value)
        self.name_entry = ttk.Entry(main_frame, textvariable=self.name_var, width=30)
        self.name_entry.pack(fill=tk.X, pady=(0, 20))
        self.name_entry.focus()

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="موافق", command=self.ok_clicked).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.RIGHT)

        # ربط مفتاح Enter
        self.name_entry.bind('<Return>', lambda e: self.ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())

        # انتظار النتيجة
        self.dialog.wait_window()

    def ok_clicked(self):
        """عند النقر على موافق"""
        name = self.name_var.get().strip()
        if name:
            self.result = name
            self.dialog.destroy()
        else:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم العامل")

    def cancel_clicked(self):
        """عند النقر على إلغاء"""
        self.dialog.destroy()
