# -*- coding: utf-8 -*-
"""
نافذة التقارير والإحصائيات - مختبر الصحة العامة المركزي ذي قار
Reports and Statistics Window - Central Public Health Laboratory
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date, timedelta
from typing import Dict, List, Any
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
from collections import defaultdict

class ReportsWindow:
    def __init__(self, parent, db_manager, main_app):
        """تهيئة نافذة التقارير والإحصائيات"""
        self.parent = parent
        self.db_manager = db_manager
        self.main_app = main_app
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_variables()
        self.setup_interface()
        self.load_data()
        
        # تسجيل كمراقب للتحديثات
        self.main_app.add_observer(self)
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("التقارير والإحصائيات - مختبر الصحة العامة المركزي")
        self.window.geometry("1400x900")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
    
    def setup_variables(self):
        """إعداد المتغيرات"""
        # متغيرات الفلترة
        self.filter_date_from_var = tk.StringVar()
        self.filter_date_to_var = tk.StringVar()
        self.filter_name_var = tk.StringVar()
        self.filter_sample_type_var = tk.StringVar()
        self.filter_gender_var = tk.StringVar()
        self.filter_sending_entity_var = tk.StringVar()
        self.filter_test_var = tk.StringVar()
        self.filter_result_var = tk.StringVar()
        
        # متغيرات قراءة الباركود
        self.barcode_var = tk.StringVar()
        
        # قوائم البيانات
        self.sample_types = []
        self.sending_entities = []
        self.tests = []
        self.report_data = []
        
        # تعيين التواريخ الافتراضية
        today = datetime.now()
        week_ago = today - timedelta(days=7)
        self.filter_date_from_var.set(week_ago.strftime('%Y-%m-%d'))
        self.filter_date_to_var.set(today.strftime('%Y-%m-%d'))
    
    def setup_interface(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي مع تبويبات
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تبويب التقارير
        reports_frame = ttk.Frame(notebook)
        notebook.add(reports_frame, text="التقارير")
        self.setup_reports_tab(reports_frame)
        
        # تبويب الإحصائيات
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="الإحصائيات")
        self.setup_statistics_tab(stats_frame)
        
        # تبويب قراءة الباركود
        barcode_frame = ttk.Frame(notebook)
        notebook.add(barcode_frame, text="قراءة الباركود")
        self.setup_barcode_tab(barcode_frame)
    
    def setup_reports_tab(self, parent):
        """إعداد تبويب التقارير"""
        # إطار الفلاتر
        filters_frame = ttk.LabelFrame(parent, text="فلاتر التقرير", padding="10")
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.setup_filters_section(filters_frame)
        
        # إطار النتائج
        results_frame = ttk.LabelFrame(parent, text="نتائج التقرير", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.setup_results_section(results_frame)
        
        # إطار العمليات
        operations_frame = ttk.LabelFrame(parent, text="عمليات التقرير", padding="10")
        operations_frame.pack(fill=tk.X)
        
        self.setup_report_operations(operations_frame)
    
    def setup_filters_section(self, parent):
        """إعداد قسم الفلاتر"""
        # الصف الأول - التواريخ
        date_frame = ttk.Frame(parent)
        date_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(date_frame, text="من تاريخ:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        date_from_entry = ttk.Entry(date_frame, textvariable=self.filter_date_from_var, width=12)
        date_from_entry.grid(row=0, column=1, padx=(0, 20))
        
        ttk.Label(date_frame, text="إلى تاريخ:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        date_to_entry = ttk.Entry(date_frame, textvariable=self.filter_date_to_var, width=12)
        date_to_entry.grid(row=0, column=3, padx=(0, 20))
        
        # أزرار سريعة للتواريخ
        ttk.Button(date_frame, text="اليوم", command=self.set_today_filter).grid(row=0, column=4, padx=5)
        ttk.Button(date_frame, text="أمس", command=self.set_yesterday_filter).grid(row=0, column=5, padx=5)
        ttk.Button(date_frame, text="هذا الأسبوع", command=self.set_week_filter).grid(row=0, column=6, padx=5)
        ttk.Button(date_frame, text="هذا الشهر", command=self.set_month_filter).grid(row=0, column=7, padx=5)
        
        # الصف الثاني - فلاتر أخرى
        other_filters = ttk.Frame(parent)
        other_filters.pack(fill=tk.X, pady=(0, 10))
        
        # اسم المريض
        ttk.Label(other_filters, text="الاسم:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        name_entry = ttk.Entry(other_filters, textvariable=self.filter_name_var, width=20)
        name_entry.grid(row=0, column=1, padx=(0, 20))
        
        # نوع العينة
        ttk.Label(other_filters, text="نوع العينة:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.sample_type_filter_combo = ttk.Combobox(other_filters, textvariable=self.filter_sample_type_var,
                                                    state='readonly', width=15)
        self.sample_type_filter_combo.grid(row=0, column=3, padx=(0, 20))
        
        # الجنس
        ttk.Label(other_filters, text="الجنس:").grid(row=0, column=4, sticky=tk.W, padx=(0, 5))
        gender_combo = ttk.Combobox(other_filters, textvariable=self.filter_gender_var,
                                   values=['', 'M', 'F'], state='readonly', width=8)
        gender_combo.grid(row=0, column=5, padx=(0, 20))
        
        # الصف الثالث - فلاتر إضافية
        additional_filters = ttk.Frame(parent)
        additional_filters.pack(fill=tk.X, pady=(0, 10))
        
        # جهة الإرسال
        ttk.Label(additional_filters, text="جهة الإرسال:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.sending_entity_filter_combo = ttk.Combobox(additional_filters, textvariable=self.filter_sending_entity_var,
                                                       state='readonly', width=20)
        self.sending_entity_filter_combo.grid(row=0, column=1, padx=(0, 20))
        
        # التحليل
        ttk.Label(additional_filters, text="التحليل:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.test_filter_combo = ttk.Combobox(additional_filters, textvariable=self.filter_test_var,
                                             state='readonly', width=15)
        self.test_filter_combo.grid(row=0, column=3, padx=(0, 20))
        
        # النتيجة
        ttk.Label(additional_filters, text="النتيجة:").grid(row=0, column=4, sticky=tk.W, padx=(0, 5))
        result_combo = ttk.Combobox(additional_filters, textvariable=self.filter_result_var,
                                   values=['', 'Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND'],
                                   state='readonly', width=12)
        result_combo.grid(row=0, column=5, padx=(0, 20))
        
        # أزرار الفلاتر
        filter_buttons = ttk.Frame(parent)
        filter_buttons.pack(fill=tk.X)
        
        ttk.Button(filter_buttons, text="تطبيق الفلاتر", 
                  command=self.apply_filters, 
                  style='Accent.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(filter_buttons, text="مسح الفلاتر", 
                  command=self.clear_filters).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(filter_buttons, text="حفظ الفلاتر", 
                  command=self.save_filters).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(filter_buttons, text="تحميل الفلاتر", 
                  command=self.load_filters).pack(side=tk.LEFT)
    
    def setup_results_section(self, parent):
        """إعداد قسم النتائج"""
        # إطار الجدول
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الجدول
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 
                  'جهة الإرسال', 'التحليل', 'النتيجة', 'تاريخ السحب', 'تاريخ النتيجة')
        
        self.report_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=20)
        
        # تعيين عناوين الأعمدة
        column_widths = [100, 150, 60, 60, 100, 120, 120, 80, 100, 100]
        for i, col in enumerate(columns):
            self.report_tree.heading(col, text=col)
            self.report_tree.column(col, width=column_widths[i], anchor=tk.CENTER)
        
        # شريط التمرير
        tree_scrollbar_y = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.report_tree.yview)
        tree_scrollbar_x = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.report_tree.xview)
        self.report_tree.config(yscrollcommand=tree_scrollbar_y.set, xscrollcommand=tree_scrollbar_x.set)
        
        # تخطيط الجدول
        self.report_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # ربط الأحداث
        self.report_tree.bind('<Double-1>', self.on_report_item_double_click)
    
    def setup_report_operations(self, parent):
        """إعداد عمليات التقرير"""
        # أزرار العمليات
        operations_frame = ttk.Frame(parent)
        operations_frame.pack(fill=tk.X)
        
        ttk.Button(operations_frame, text="طباعة التقرير", 
                  command=self.print_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(operations_frame, text="تصدير إلى Excel", 
                  command=self.export_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(operations_frame, text="تصدير إلى PDF", 
                  command=self.export_pdf).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(operations_frame, text="طباعة تقرير فردي", 
                  command=self.print_individual_report).pack(side=tk.LEFT, padx=(0, 10))
        
        # معلومات التقرير
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.report_info_label = ttk.Label(info_frame, text="عدد السجلات: 0")
        self.report_info_label.pack(side=tk.LEFT)
    
    def setup_statistics_tab(self, parent):
        """إعداد تبويب الإحصائيات"""
        # إطار الإحصائيات النصية
        text_stats_frame = ttk.LabelFrame(parent, text="الإحصائيات العامة", padding="10")
        text_stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.setup_text_statistics(text_stats_frame)
        
        # إطار الرسوم البيانية
        charts_frame = ttk.LabelFrame(parent, text="الرسوم البيانية", padding="10")
        charts_frame.pack(fill=tk.BOTH, expand=True)
        
        self.setup_charts_section(charts_frame)
    
    def setup_text_statistics(self, parent):
        """إعداد الإحصائيات النصية"""
        # إطار الإحصائيات
        stats_grid = ttk.Frame(parent)
        stats_grid.pack(fill=tk.X)
        
        # إحصائيات العينات
        samples_frame = ttk.LabelFrame(stats_grid, text="إحصائيات العينات", padding="10")
        samples_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        self.total_samples_label = ttk.Label(samples_frame, text="إجمالي العينات: 0")
        self.total_samples_label.pack(anchor=tk.W)
        
        self.today_samples_label = ttk.Label(samples_frame, text="عينات اليوم: 0")
        self.today_samples_label.pack(anchor=tk.W)
        
        self.week_samples_label = ttk.Label(samples_frame, text="عينات الأسبوع: 0")
        self.week_samples_label.pack(anchor=tk.W)
        
        # إحصائيات النتائج
        results_frame = ttk.LabelFrame(stats_grid, text="إحصائيات النتائج", padding="10")
        results_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        self.total_results_label = ttk.Label(results_frame, text="إجمالي النتائج: 0")
        self.total_results_label.pack(anchor=tk.W)
        
        self.positive_results_label = ttk.Label(results_frame, text="النتائج الإيجابية: 0")
        self.positive_results_label.pack(anchor=tk.W)
        
        self.negative_results_label = ttk.Label(results_frame, text="النتائج السلبية: 0")
        self.negative_results_label.pack(anchor=tk.W)
        
        self.pending_results_label = ttk.Label(results_frame, text="النتائج المعلقة: 0")
        self.pending_results_label.pack(anchor=tk.W)
        
        # إحصائيات التحاليل
        tests_frame = ttk.LabelFrame(stats_grid, text="إحصائيات التحاليل", padding="10")
        tests_frame.grid(row=0, column=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.most_common_test_label = ttk.Label(tests_frame, text="أكثر التحاليل طلباً: -")
        self.most_common_test_label.pack(anchor=tk.W)
        
        self.completion_rate_label = ttk.Label(tests_frame, text="معدل الإنجاز: 0%")
        self.completion_rate_label.pack(anchor=tk.W)
        
        # تكوين الشبكة
        stats_grid.columnconfigure(0, weight=1)
        stats_grid.columnconfigure(1, weight=1)
        stats_grid.columnconfigure(2, weight=1)
        
        # زر تحديث الإحصائيات
        ttk.Button(parent, text="تحديث الإحصائيات", 
                  command=self.update_statistics).pack(pady=(10, 0))
    
    def setup_charts_section(self, parent):
        """إعداد قسم الرسوم البيانية"""
        # إطار أزرار الرسوم البيانية
        charts_buttons = ttk.Frame(parent)
        charts_buttons.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(charts_buttons, text="رسم بياني للعينات اليومية", 
                  command=self.show_daily_samples_chart).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(charts_buttons, text="رسم بياني للنتائج", 
                  command=self.show_results_chart).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(charts_buttons, text="رسم بياني للتحاليل", 
                  command=self.show_tests_chart).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(charts_buttons, text="رسم بياني لجهات الإرسال", 
                  command=self.show_sending_entities_chart).pack(side=tk.LEFT)
        
        # إطار الرسم البياني
        self.chart_frame = ttk.Frame(parent)
        self.chart_frame.pack(fill=tk.BOTH, expand=True)
    
    def setup_barcode_tab(self, parent):
        """إعداد تبويب قراءة الباركود"""
        # إطار إدخال الباركود
        barcode_input_frame = ttk.LabelFrame(parent, text="قراءة الباركود", padding="20")
        barcode_input_frame.pack(fill=tk.X, pady=(0, 20))
        
        # حقل إدخال الباركود
        input_frame = ttk.Frame(barcode_input_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(input_frame, text="الباركود:", font=('Arial', 12)).pack(side=tk.LEFT, padx=(0, 10))
        barcode_entry = ttk.Entry(input_frame, textvariable=self.barcode_var, 
                                 font=('Arial', 14), width=30)
        barcode_entry.pack(side=tk.LEFT, padx=(0, 10))
        barcode_entry.bind('<Return>', self.search_by_barcode)
        barcode_entry.focus()
        
        ttk.Button(input_frame, text="بحث", 
                  command=self.search_by_barcode).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(input_frame, text="مسح", 
                  command=self.clear_barcode).pack(side=tk.LEFT)
        
        # معلومات الباركود
        info_text = """
        تعليمات قراءة الباركود:
        1. ضع المؤشر في حقل الباركود
        2. امسح الباركود باستخدام قارئ الباركود
        3. أو اكتب رقم الباركود يدوياً
        4. اضغط Enter أو زر البحث
        """
        
        ttk.Label(barcode_input_frame, text=info_text, 
                 font=('Arial', 10), foreground='gray').pack(anchor=tk.W)
        
        # إطار نتائج البحث
        barcode_results_frame = ttk.LabelFrame(parent, text="نتائج البحث", padding="10")
        barcode_results_frame.pack(fill=tk.BOTH, expand=True)
        
        # جدول نتائج الباركود
        barcode_columns = ('الرقم الوطني', 'الاسم', 'نوع العينة', 'التحاليل', 'الحالة')
        self.barcode_tree = ttk.Treeview(barcode_results_frame, columns=barcode_columns, 
                                        show='headings', height=15)
        
        for col in barcode_columns:
            self.barcode_tree.heading(col, text=col)
            self.barcode_tree.column(col, width=150, anchor=tk.CENTER)
        
        # شريط التمرير للباركود
        barcode_scrollbar = ttk.Scrollbar(barcode_results_frame, orient=tk.VERTICAL, 
                                         command=self.barcode_tree.yview)
        self.barcode_tree.config(yscrollcommand=barcode_scrollbar.set)
        
        self.barcode_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        barcode_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def load_data(self):
        """تحميل البيانات الأولية"""
        # تحميل أنواع العينات
        self.sample_types = self.db_manager.get_sample_types()
        sample_type_names = [''] + [item['name'] for item in self.sample_types]
        self.sample_type_filter_combo['values'] = sample_type_names

        # تحميل جهات الإرسال
        self.sending_entities = self.db_manager.get_sending_entities()
        sending_entity_names = [''] + [item['name'] for item in self.sending_entities]
        self.sending_entity_filter_combo['values'] = sending_entity_names

        # تحميل التحاليل
        self.tests = self.db_manager.get_tests()
        test_names = [''] + [test['name'] for test in self.tests]
        self.test_filter_combo['values'] = test_names

        # تحميل التقرير الافتراضي
        self.apply_filters()

        # تحديث الإحصائيات
        self.update_statistics()

    # دوال الفلاتر السريعة
    def set_today_filter(self):
        """تعيين فلتر اليوم"""
        today = datetime.now().strftime('%Y-%m-%d')
        self.filter_date_from_var.set(today)
        self.filter_date_to_var.set(today)

    def set_yesterday_filter(self):
        """تعيين فلتر أمس"""
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        self.filter_date_from_var.set(yesterday)
        self.filter_date_to_var.set(yesterday)

    def set_week_filter(self):
        """تعيين فلتر هذا الأسبوع"""
        today = datetime.now()
        start_of_week = today - timedelta(days=today.weekday())
        self.filter_date_from_var.set(start_of_week.strftime('%Y-%m-%d'))
        self.filter_date_to_var.set(today.strftime('%Y-%m-%d'))

    def set_month_filter(self):
        """تعيين فلتر هذا الشهر"""
        today = datetime.now()
        start_of_month = today.replace(day=1)
        self.filter_date_from_var.set(start_of_month.strftime('%Y-%m-%d'))
        self.filter_date_to_var.set(today.strftime('%Y-%m-%d'))

    def apply_filters(self):
        """تطبيق الفلاتر وتحميل التقرير"""
        try:
            # بناء الاستعلام
            query = """
                SELECT
                    p.national_number,
                    p.name as patient_name,
                    p.age,
                    p.gender,
                    st.name as sample_type,
                    se.name as sending_entity,
                    t.name as test_name,
                    r.result,
                    p.sample_collection_date,
                    r.result_date,
                    p.sample_received_date,
                    r.notes
                FROM patients p
                JOIN sample_types st ON p.sample_type_id = st.id
                JOIN sending_entities se ON p.sending_entity_id = se.id
                JOIN patient_tests pt ON p.id = pt.patient_id
                JOIN tests t ON pt.test_id = t.id
                LEFT JOIN results r ON p.id = r.patient_id AND t.id = r.test_id
                WHERE 1=1
            """

            params = []

            # فلتر التاريخ
            if self.filter_date_from_var.get():
                query += " AND DATE(p.sample_received_date) >= ?"
                params.append(self.filter_date_from_var.get())

            if self.filter_date_to_var.get():
                query += " AND DATE(p.sample_received_date) <= ?"
                params.append(self.filter_date_to_var.get())

            # فلتر الاسم
            if self.filter_name_var.get().strip():
                query += " AND p.name LIKE ?"
                params.append(f"%{self.filter_name_var.get().strip()}%")

            # فلتر نوع العينة
            if self.filter_sample_type_var.get():
                query += " AND st.name = ?"
                params.append(self.filter_sample_type_var.get())

            # فلتر الجنس
            if self.filter_gender_var.get():
                query += " AND p.gender = ?"
                params.append(self.filter_gender_var.get())

            # فلتر جهة الإرسال
            if self.filter_sending_entity_var.get():
                query += " AND se.name = ?"
                params.append(self.filter_sending_entity_var.get())

            # فلتر التحليل
            if self.filter_test_var.get():
                query += " AND t.name = ?"
                params.append(self.filter_test_var.get())

            # فلتر النتيجة
            if self.filter_result_var.get():
                if self.filter_result_var.get() == 'معلق':
                    query += " AND r.result IS NULL"
                else:
                    query += " AND r.result = ?"
                    params.append(self.filter_result_var.get())

            query += " ORDER BY p.sample_received_date DESC, p.national_number"

            # تنفيذ الاستعلام
            self.report_data = self.db_manager.execute_query(query, tuple(params))

            # مسح الجدول الحالي
            for item in self.report_tree.get_children():
                self.report_tree.delete(item)

            # عرض البيانات
            for record in self.report_data:
                gender_text = 'ذكر' if record['gender'] == 'M' else 'أنثى'
                result_text = record['result'] if record['result'] else 'معلق'
                result_date = record['result_date'][:10] if record['result_date'] else ''

                self.report_tree.insert('', tk.END, values=(
                    record['national_number'],
                    record['patient_name'],
                    record['age'],
                    gender_text,
                    record['sample_type'],
                    record['sending_entity'],
                    record['test_name'],
                    result_text,
                    record['sample_collection_date'],
                    result_date
                ))

            # تحديث معلومات التقرير
            self.report_info_label.config(text=f"عدد السجلات: {len(self.report_data)}")

            self.main_app.update_status(f"تم تحميل {len(self.report_data)} سجل في التقرير")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تطبيق الفلاتر: {str(e)}")

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        today = datetime.now()
        week_ago = today - timedelta(days=7)

        self.filter_date_from_var.set(week_ago.strftime('%Y-%m-%d'))
        self.filter_date_to_var.set(today.strftime('%Y-%m-%d'))
        self.filter_name_var.set("")
        self.filter_sample_type_var.set("")
        self.filter_gender_var.set("")
        self.filter_sending_entity_var.set("")
        self.filter_test_var.set("")
        self.filter_result_var.set("")

        # إعادة تطبيق الفلاتر
        self.apply_filters()

    def save_filters(self):
        """حفظ الفلاتر الحالية"""
        try:
            import json

            filters = {
                'date_from': self.filter_date_from_var.get(),
                'date_to': self.filter_date_to_var.get(),
                'name': self.filter_name_var.get(),
                'sample_type': self.filter_sample_type_var.get(),
                'gender': self.filter_gender_var.get(),
                'sending_entity': self.filter_sending_entity_var.get(),
                'test': self.filter_test_var.get(),
                'result': self.filter_result_var.get()
            }

            file_path = filedialog.asksaveasfilename(
                title="حفظ الفلاتر",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json")]
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(filters, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("نجح", "تم حفظ الفلاتر بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الفلاتر: {str(e)}")

    def load_filters(self):
        """تحميل الفلاتر المحفوظة"""
        try:
            import json

            file_path = filedialog.askopenfilename(
                title="تحميل الفلاتر",
                filetypes=[("JSON files", "*.json")]
            )

            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    filters = json.load(f)

                # تطبيق الفلاتر
                self.filter_date_from_var.set(filters.get('date_from', ''))
                self.filter_date_to_var.set(filters.get('date_to', ''))
                self.filter_name_var.set(filters.get('name', ''))
                self.filter_sample_type_var.set(filters.get('sample_type', ''))
                self.filter_gender_var.set(filters.get('gender', ''))
                self.filter_sending_entity_var.set(filters.get('sending_entity', ''))
                self.filter_test_var.set(filters.get('test', ''))
                self.filter_result_var.set(filters.get('result', ''))

                # إعادة تطبيق الفلاتر
                self.apply_filters()

                messagebox.showinfo("نجح", "تم تحميل الفلاتر بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الفلاتر: {str(e)}")

    # دوال التقارير والطباعة
    def print_report(self):
        """طباعة التقرير"""
        if not self.report_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للطباعة")
            return

        try:
            from reports_printer import ReportsPrinter

            printer = ReportsPrinter()
            printer.print_report(self.report_data, self.get_filter_summary())

            self.main_app.update_status("تم إرسال التقرير للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {str(e)}")

    def export_report(self):
        """تصدير التقرير إلى Excel"""
        if not self.report_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="حفظ التقرير",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")]
            )

            if file_path:
                # إعداد البيانات للتصدير
                export_data = []
                for record in self.report_data:
                    export_data.append({
                        'الرقم الوطني': record['national_number'],
                        'اسم المريض': record['patient_name'],
                        'العمر': record['age'],
                        'الجنس': 'ذكر' if record['gender'] == 'M' else 'أنثى',
                        'نوع العينة': record['sample_type'],
                        'جهة الإرسال': record['sending_entity'],
                        'التحليل': record['test_name'],
                        'النتيجة': record['result'] if record['result'] else 'معلق',
                        'تاريخ سحب العينة': record['sample_collection_date'],
                        'تاريخ النتيجة': record['result_date'][:10] if record['result_date'] else '',
                        'ملاحظات': record['notes'] if record['notes'] else ''
                    })

                # تحويل إلى DataFrame وحفظ
                df = pd.DataFrame(export_data)
                df.to_excel(file_path, index=False, engine='openpyxl')

                messagebox.showinfo("نجح", f"تم تصدير {len(export_data)} سجل إلى Excel بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقرير: {str(e)}")

    def export_pdf(self):
        """تصدير التقرير إلى PDF"""
        if not self.report_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return

        try:
            from reports_printer import ReportsPrinter

            file_path = filedialog.asksaveasfilename(
                title="حفظ التقرير PDF",
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")]
            )

            if file_path:
                printer = ReportsPrinter()
                printer.export_to_pdf(self.report_data, self.get_filter_summary(), file_path)

                messagebox.showinfo("نجح", "تم تصدير التقرير إلى PDF بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير PDF: {str(e)}")

    def print_individual_report(self):
        """طباعة تقرير فردي"""
        selected_items = self.report_tree.selection()
        if not selected_items:
            messagebox.showwarning("تحذير", "يرجى اختيار سجل لطباعة تقرير فردي")
            return

        try:
            from results_printer import ResultsPrinter

            # الحصول على بيانات السجل المحدد
            item = selected_items[0]
            values = self.report_tree.item(item)['values']
            national_number = values[0]
            test_name = values[6]

            # البحث عن السجل في البيانات
            selected_record = None
            for record in self.report_data:
                if (record['national_number'] == national_number and
                    record['test_name'] == test_name):
                    selected_record = record
                    break

            if selected_record:
                printer = ResultsPrinter()
                printer.print_single_result(selected_record)

                self.main_app.update_status("تم إرسال التقرير الفردي للطباعة")
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على السجل المحدد")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير الفردي: {str(e)}")

    def get_filter_summary(self):
        """الحصول على ملخص الفلاتر المطبقة"""
        summary = []

        if self.filter_date_from_var.get():
            summary.append(f"من تاريخ: {self.filter_date_from_var.get()}")

        if self.filter_date_to_var.get():
            summary.append(f"إلى تاريخ: {self.filter_date_to_var.get()}")

        if self.filter_name_var.get():
            summary.append(f"الاسم: {self.filter_name_var.get()}")

        if self.filter_sample_type_var.get():
            summary.append(f"نوع العينة: {self.filter_sample_type_var.get()}")

        if self.filter_gender_var.get():
            gender_text = 'ذكر' if self.filter_gender_var.get() == 'M' else 'أنثى'
            summary.append(f"الجنس: {gender_text}")

        if self.filter_sending_entity_var.get():
            summary.append(f"جهة الإرسال: {self.filter_sending_entity_var.get()}")

        if self.filter_test_var.get():
            summary.append(f"التحليل: {self.filter_test_var.get()}")

        if self.filter_result_var.get():
            summary.append(f"النتيجة: {self.filter_result_var.get()}")

        return summary

    def on_report_item_double_click(self, event):
        """النقر المزدوج على عنصر في التقرير"""
        self.print_individual_report()

    # دوال الإحصائيات
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # إحصائيات العينات
            total_samples = self.db_manager.execute_query("SELECT COUNT(*) as count FROM patients")[0]['count']

            today = datetime.now().strftime('%Y-%m-%d')
            today_samples = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM patients WHERE DATE(sample_received_date) = ?",
                (today,)
            )[0]['count']

            week_start = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            week_samples = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM patients WHERE DATE(sample_received_date) >= ?",
                (week_start,)
            )[0]['count']

            # إحصائيات النتائج
            total_results = self.db_manager.execute_query("SELECT COUNT(*) as count FROM results")[0]['count']

            positive_results = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM results WHERE result = 'Positive'"
            )[0]['count']

            negative_results = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM results WHERE result = 'Negative'"
            )[0]['count']

            pending_results = self.db_manager.execute_query("""
                SELECT COUNT(*) as count FROM patient_tests pt
                LEFT JOIN results r ON pt.patient_id = r.patient_id AND pt.test_id = r.test_id
                WHERE r.id IS NULL
            """)[0]['count']

            # أكثر التحاليل طلباً
            most_common_test = self.db_manager.execute_query("""
                SELECT t.name, COUNT(*) as count
                FROM patient_tests pt
                JOIN tests t ON pt.test_id = t.id
                GROUP BY t.name
                ORDER BY count DESC
                LIMIT 1
            """)

            most_common_test_name = most_common_test[0]['name'] if most_common_test else 'لا يوجد'

            # معدل الإنجاز
            total_tests = self.db_manager.execute_query("SELECT COUNT(*) as count FROM patient_tests")[0]['count']
            completion_rate = (total_results / total_tests * 100) if total_tests > 0 else 0

            # تحديث التسميات
            self.total_samples_label.config(text=f"إجمالي العينات: {total_samples}")
            self.today_samples_label.config(text=f"عينات اليوم: {today_samples}")
            self.week_samples_label.config(text=f"عينات الأسبوع: {week_samples}")

            self.total_results_label.config(text=f"إجمالي النتائج: {total_results}")
            self.positive_results_label.config(text=f"النتائج الإيجابية: {positive_results}")
            self.negative_results_label.config(text=f"النتائج السلبية: {negative_results}")
            self.pending_results_label.config(text=f"النتائج المعلقة: {pending_results}")

            self.most_common_test_label.config(text=f"أكثر التحاليل طلباً: {most_common_test_name}")
            self.completion_rate_label.config(text=f"معدل الإنجاز: {completion_rate:.1f}%")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث الإحصائيات: {str(e)}")

    # دوال الرسوم البيانية
    def show_daily_samples_chart(self):
        """عرض رسم بياني للعينات اليومية"""
        try:
            # مسح الرسم البياني السابق
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # الحصول على بيانات العينات اليومية للأسبوعين الماضيين
            end_date = datetime.now()
            start_date = end_date - timedelta(days=14)

            query = """
                SELECT DATE(sample_received_date) as date, COUNT(*) as count
                FROM patients
                WHERE DATE(sample_received_date) BETWEEN ? AND ?
                GROUP BY DATE(sample_received_date)
                ORDER BY date
            """

            data = self.db_manager.execute_query(query, (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))

            if not data:
                ttk.Label(self.chart_frame, text="لا توجد بيانات لعرض الرسم البياني").pack(expand=True)
                return

            # إعداد البيانات
            dates = [datetime.strptime(item['date'], '%Y-%m-%d') for item in data]
            counts = [item['count'] for item in data]

            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.plot(dates, counts, marker='o', linewidth=2, markersize=6)
            ax.set_title('العينات اليومية - الأسبوعين الماضيين', fontsize=14, pad=20)
            ax.set_xlabel('التاريخ')
            ax.set_ylabel('عدد العينات')
            ax.grid(True, alpha=0.3)

            # تنسيق التواريخ
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
            plt.xticks(rotation=45)

            plt.tight_layout()

            # عرض الرسم البياني
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض الرسم البياني: {str(e)}")

    def show_results_chart(self):
        """عرض رسم بياني للنتائج"""
        try:
            # مسح الرسم البياني السابق
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # الحصول على بيانات النتائج
            query = """
                SELECT result, COUNT(*) as count
                FROM results
                WHERE result IS NOT NULL
                GROUP BY result
                ORDER BY count DESC
            """

            data = self.db_manager.execute_query(query)

            if not data:
                ttk.Label(self.chart_frame, text="لا توجد نتائج لعرض الرسم البياني").pack(expand=True)
                return

            # إعداد البيانات
            results = [item['result'] for item in data]
            counts = [item['count'] for item in data]

            # ترجمة النتائج
            result_translations = {
                'Positive': 'إيجابي',
                'Negative': 'سلبي',
                'Retest': 'إعادة فحص',
                'Recollection': 'إعادة سحب',
                'Sent': 'مُرسل',
                'TND': 'غير محدد'
            }

            translated_results = [result_translations.get(result, result) for result in results]

            # إنشاء الرسم البياني الدائري
            fig, ax = plt.subplots(figsize=(10, 8))
            colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc', '#c2c2f0']

            wedges, texts, autotexts = ax.pie(counts, labels=translated_results, autopct='%1.1f%%',
                                             colors=colors[:len(counts)], startangle=90)

            ax.set_title('توزيع النتائج', fontsize=14, pad=20)

            # تحسين النص
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')

            plt.tight_layout()

            # عرض الرسم البياني
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض رسم النتائج: {str(e)}")

    def show_tests_chart(self):
        """عرض رسم بياني للتحاليل"""
        try:
            # مسح الرسم البياني السابق
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # الحصول على بيانات التحاليل
            query = """
                SELECT t.name, COUNT(*) as count
                FROM patient_tests pt
                JOIN tests t ON pt.test_id = t.id
                GROUP BY t.name
                ORDER BY count DESC
                LIMIT 10
            """

            data = self.db_manager.execute_query(query)

            if not data:
                ttk.Label(self.chart_frame, text="لا توجد تحاليل لعرض الرسم البياني").pack(expand=True)
                return

            # إعداد البيانات
            tests = [item['name'] for item in data]
            counts = [item['count'] for item in data]

            # إنشاء الرسم البياني العمودي
            fig, ax = plt.subplots(figsize=(12, 8))
            bars = ax.bar(range(len(tests)), counts, color='skyblue', alpha=0.7)

            ax.set_title('أكثر التحاليل طلباً (أعلى 10)', fontsize=14, pad=20)
            ax.set_xlabel('التحاليل')
            ax.set_ylabel('عدد الطلبات')
            ax.set_xticks(range(len(tests)))
            ax.set_xticklabels(tests, rotation=45, ha='right')
            ax.grid(True, alpha=0.3, axis='y')

            # إضافة القيم على الأعمدة
            for bar, count in zip(bars, counts):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                       str(count), ha='center', va='bottom')

            plt.tight_layout()

            # عرض الرسم البياني
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض رسم التحاليل: {str(e)}")

    def show_sending_entities_chart(self):
        """عرض رسم بياني لجهات الإرسال"""
        try:
            # مسح الرسم البياني السابق
            for widget in self.chart_frame.winfo_children():
                widget.destroy()

            # الحصول على بيانات جهات الإرسال
            query = """
                SELECT se.name, COUNT(*) as count
                FROM patients p
                JOIN sending_entities se ON p.sending_entity_id = se.id
                GROUP BY se.name
                ORDER BY count DESC
            """

            data = self.db_manager.execute_query(query)

            if not data:
                ttk.Label(self.chart_frame, text="لا توجد جهات إرسال لعرض الرسم البياني").pack(expand=True)
                return

            # إعداد البيانات
            entities = [item['name'] for item in data]
            counts = [item['count'] for item in data]

            # إنشاء الرسم البياني الأفقي
            fig, ax = plt.subplots(figsize=(10, 8))
            bars = ax.barh(range(len(entities)), counts, color='lightcoral', alpha=0.7)

            ax.set_title('توزيع العينات حسب جهة الإرسال', fontsize=14, pad=20)
            ax.set_xlabel('عدد العينات')
            ax.set_ylabel('جهة الإرسال')
            ax.set_yticks(range(len(entities)))
            ax.set_yticklabels(entities)
            ax.grid(True, alpha=0.3, axis='x')

            # إضافة القيم على الأعمدة
            for bar, count in zip(bars, counts):
                width = bar.get_width()
                ax.text(width + 0.5, bar.get_y() + bar.get_height()/2.,
                       str(count), ha='left', va='center')

            plt.tight_layout()

            # عرض الرسم البياني
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض رسم جهات الإرسال: {str(e)}")

    # دوال قراءة الباركود
    def search_by_barcode(self, event=None):
        """البحث بالباركود"""
        barcode = self.barcode_var.get().strip()

        if not barcode:
            messagebox.showwarning("تحذير", "يرجى إدخال الباركود")
            return

        try:
            # مسح النتائج السابقة
            for item in self.barcode_tree.get_children():
                self.barcode_tree.delete(item)

            # البحث عن المريض بالباركود
            query = """
                SELECT
                    p.national_number,
                    p.name,
                    st.name as sample_type,
                    GROUP_CONCAT(t.name) as tests,
                    CASE
                        WHEN COUNT(r.id) = 0 THEN 'لا توجد نتائج'
                        WHEN COUNT(r.id) = COUNT(pt.id) THEN 'مكتملة'
                        ELSE 'جزئية'
                    END as status
                FROM patients p
                JOIN sample_types st ON p.sample_type_id = st.id
                LEFT JOIN patient_tests pt ON p.id = pt.patient_id
                LEFT JOIN tests t ON pt.test_id = t.id
                LEFT JOIN results r ON p.id = r.patient_id AND t.id = r.test_id
                WHERE p.barcode = ?
                GROUP BY p.id
            """

            results = self.db_manager.execute_query(query, (barcode,))

            if results:
                for result in results:
                    self.barcode_tree.insert('', tk.END, values=(
                        result['national_number'],
                        result['name'],
                        result['sample_type'],
                        result['tests'] if result['tests'] else 'لا توجد',
                        result['status']
                    ))

                self.main_app.update_status(f"تم العثور على {len(results)} نتيجة للباركود: {barcode}")
            else:
                messagebox.showinfo("لا توجد نتائج", f"لم يتم العثور على عينة بالباركود: {barcode}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث بالباركود: {str(e)}")

    def clear_barcode(self):
        """مسح الباركود"""
        self.barcode_var.set("")

        # مسح النتائج
        for item in self.barcode_tree.get_children():
            self.barcode_tree.delete(item)

    # دوال التزامن
    def on_data_changed(self, event_type: str, data: dict = None):
        """استقبال إشعارات التحديث من النوافذ الأخرى"""
        if event_type in ['patient_added', 'patient_updated', 'patient_deleted',
                         'result_saved', 'result_deleted', 'bulk_results_saved']:
            # إعادة تطبيق الفلاتر وتحديث الإحصائيات
            self.apply_filters()
            self.update_statistics()
        elif event_type in ['sample_type_added', 'sample_type_updated', 'sample_type_deleted']:
            # إعادة تحميل أنواع العينات
            self.sample_types = self.db_manager.get_sample_types()
            sample_type_names = [''] + [item['name'] for item in self.sample_types]
            self.sample_type_filter_combo['values'] = sample_type_names
        elif event_type in ['sending_entity_added', 'sending_entity_updated', 'sending_entity_deleted']:
            # إعادة تحميل جهات الإرسال
            self.sending_entities = self.db_manager.get_sending_entities()
            sending_entity_names = [''] + [item['name'] for item in self.sending_entities]
            self.sending_entity_filter_combo['values'] = sending_entity_names
        elif event_type in ['test_added', 'test_updated', 'test_deleted']:
            # إعادة تحميل التحاليل
            self.tests = self.db_manager.get_tests()
            test_names = [''] + [test['name'] for test in self.tests]
            self.test_filter_combo['values'] = test_names

    def __del__(self):
        """تنظيف الموارد عند إغلاق النافذة"""
        try:
            if hasattr(self, 'main_app') and self in self.main_app.observers:
                self.main_app.observers.remove(self)
        except:
            pass
