# -*- coding: utf-8 -*-
"""
نافذة الإعدادات - مختبر الصحة العامة المركزي ذي قار
Settings Window - Central Public Health Laboratory
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
from typing import Dict, List, Any
import json
import os

class SettingsWindow:
    def __init__(self, parent, db_manager, main_app):
        """تهيئة نافذة الإعدادات"""
        self.parent = parent
        self.db_manager = db_manager
        self.main_app = main_app
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_variables()
        self.setup_interface()
        self.load_data()
        
        # تسجيل كمراقب للتحديثات
        self.main_app.add_observer(self)
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("⚙️ الإعدادات - مختبر الصحة العامة المركزي")
        self.window.geometry("1400x900")
        self.window.resizable(True, True)

        # تعيين خلفية عصرية
        self.window.configure(bg='#f8f9fa')

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
    
    def setup_variables(self):
        """إعداد المتغيرات"""
        # متغيرات أنواع العينات
        self.sample_type_name_var = tk.StringVar()
        self.selected_sample_type_id = None
        
        # متغيرات جهات الإرسال
        self.sending_entity_name_var = tk.StringVar()
        self.selected_sending_entity_id = None
        
        # متغيرات التحاليل
        self.test_name_var = tk.StringVar()
        self.test_description_var = tk.StringVar()
        self.selected_test_id = None
        
        # قوائم البيانات
        self.sample_types = []
        self.sending_entities = []
        self.tests = []
    
    def setup_interface(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي مع تبويبات
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تبويب أنواع العينات
        sample_types_frame = ttk.Frame(notebook)
        notebook.add(sample_types_frame, text="أنواع العينات")
        self.setup_sample_types_tab(sample_types_frame)
        
        # تبويب جهات الإرسال
        sending_entities_frame = ttk.Frame(notebook)
        notebook.add(sending_entities_frame, text="جهات الإرسال")
        self.setup_sending_entities_tab(sending_entities_frame)
        
        # تبويب التحاليل
        tests_frame = ttk.Frame(notebook)
        notebook.add(tests_frame, text="التحاليل")
        self.setup_tests_tab(tests_frame)
        
        # تبويب إعدادات التقارير
        reports_settings_frame = ttk.Frame(notebook)
        notebook.add(reports_settings_frame, text="إعدادات التقارير")
        self.setup_reports_settings_tab(reports_settings_frame)
        
        # تبويب النسخ الاحتياطي
        backup_frame = ttk.Frame(notebook)
        notebook.add(backup_frame, text="النسخ الاحتياطي")
        self.setup_backup_tab(backup_frame)
    
    def setup_sample_types_tab(self, parent):
        """إعداد تبويب أنواع العينات"""
        # إطار الإدخال
        input_frame = ttk.LabelFrame(parent, text="إدارة أنواع العينات", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # حقل الاسم
        name_frame = ttk.Frame(input_frame)
        name_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(name_frame, text="اسم نوع العينة:").pack(side=tk.LEFT, padx=(0, 10))
        name_entry = ttk.Entry(name_frame, textvariable=self.sample_type_name_var, width=30)
        name_entry.pack(side=tk.LEFT, padx=(0, 20))
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(input_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="إضافة", 
                  command=self.add_sample_type).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تعديل", 
                  command=self.update_sample_type).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="حذف", 
                  command=self.delete_sample_type).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="مسح", 
                  command=self.clear_sample_type_form).pack(side=tk.LEFT)
        
        # إطار القائمة
        list_frame = ttk.LabelFrame(parent, text="قائمة أنواع العينات", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # جدول أنواع العينات
        columns = ('المعرف', 'اسم نوع العينة', 'تاريخ الإضافة')
        self.sample_types_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.sample_types_tree.heading(col, text=col)
            self.sample_types_tree.column(col, width=150, anchor=tk.CENTER)
        
        # شريط التمرير
        sample_types_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, 
                                              command=self.sample_types_tree.yview)
        self.sample_types_tree.config(yscrollcommand=sample_types_scrollbar.set)
        
        self.sample_types_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sample_types_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.sample_types_tree.bind('<Button-1>', self.on_sample_type_select)
        self.sample_types_tree.bind('<Double-1>', self.on_sample_type_double_click)
    
    def setup_sending_entities_tab(self, parent):
        """إعداد تبويب جهات الإرسال"""
        # إطار الإدخال
        input_frame = ttk.LabelFrame(parent, text="إدارة جهات الإرسال", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # حقل الاسم
        name_frame = ttk.Frame(input_frame)
        name_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(name_frame, text="اسم جهة الإرسال:").pack(side=tk.LEFT, padx=(0, 10))
        name_entry = ttk.Entry(name_frame, textvariable=self.sending_entity_name_var, width=30)
        name_entry.pack(side=tk.LEFT, padx=(0, 20))
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(input_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="إضافة", 
                  command=self.add_sending_entity).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تعديل", 
                  command=self.update_sending_entity).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="حذف", 
                  command=self.delete_sending_entity).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="مسح", 
                  command=self.clear_sending_entity_form).pack(side=tk.LEFT)
        
        # إطار القائمة
        list_frame = ttk.LabelFrame(parent, text="قائمة جهات الإرسال", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # جدول جهات الإرسال
        columns = ('المعرف', 'اسم جهة الإرسال', 'تاريخ الإضافة')
        self.sending_entities_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.sending_entities_tree.heading(col, text=col)
            self.sending_entities_tree.column(col, width=150, anchor=tk.CENTER)
        
        # شريط التمرير
        sending_entities_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, 
                                                  command=self.sending_entities_tree.yview)
        self.sending_entities_tree.config(yscrollcommand=sending_entities_scrollbar.set)
        
        self.sending_entities_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sending_entities_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.sending_entities_tree.bind('<Button-1>', self.on_sending_entity_select)
        self.sending_entities_tree.bind('<Double-1>', self.on_sending_entity_double_click)
    
    def setup_tests_tab(self, parent):
        """إعداد تبويب التحاليل"""
        # إطار الإدخال
        input_frame = ttk.LabelFrame(parent, text="إدارة التحاليل", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # حقل الاسم
        name_frame = ttk.Frame(input_frame)
        name_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(name_frame, text="اسم التحليل:").pack(side=tk.LEFT, padx=(0, 10))
        name_entry = ttk.Entry(name_frame, textvariable=self.test_name_var, width=30)
        name_entry.pack(side=tk.LEFT, padx=(0, 20))
        
        # حقل الوصف
        desc_frame = ttk.Frame(input_frame)
        desc_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(desc_frame, text="وصف التحليل:").pack(side=tk.LEFT, padx=(0, 10))
        desc_entry = ttk.Entry(desc_frame, textvariable=self.test_description_var, width=50)
        desc_entry.pack(side=tk.LEFT, padx=(0, 20))
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(input_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="إضافة", 
                  command=self.add_test).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تعديل", 
                  command=self.update_test).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="حذف", 
                  command=self.delete_test).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="مسح", 
                  command=self.clear_test_form).pack(side=tk.LEFT)
        
        # إطار القائمة
        list_frame = ttk.LabelFrame(parent, text="قائمة التحاليل", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # جدول التحاليل
        columns = ('المعرف', 'اسم التحليل', 'الوصف', 'تاريخ الإضافة')
        self.tests_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        column_widths = [80, 200, 300, 150]
        for i, col in enumerate(columns):
            self.tests_tree.heading(col, text=col)
            self.tests_tree.column(col, width=column_widths[i], anchor=tk.CENTER)
        
        # شريط التمرير
        tests_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, 
                                       command=self.tests_tree.yview)
        self.tests_tree.config(yscrollcommand=tests_scrollbar.set)
        
        self.tests_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tests_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.tests_tree.bind('<Button-1>', self.on_test_select)
        self.tests_tree.bind('<Double-1>', self.on_test_double_click)
    
    def setup_reports_settings_tab(self, parent):
        """إعداد تبويب إعدادات التقارير"""
        # إطار إعدادات التقارير
        reports_frame = ttk.LabelFrame(parent, text="إعدادات التقارير", padding="20")
        reports_frame.pack(fill=tk.X, pady=(0, 20))
        
        # معلومات المختبر
        lab_info_frame = ttk.LabelFrame(reports_frame, text="معلومات المختبر", padding="10")
        lab_info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # اسم المختبر
        ttk.Label(lab_info_frame, text="اسم المختبر:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.lab_name_var = tk.StringVar(value="مختبر الصحة العامة المركزي - ذي قار")
        ttk.Entry(lab_info_frame, textvariable=self.lab_name_var, width=50).grid(row=0, column=1, pady=5)
        
        # اسم الوزارة
        ttk.Label(lab_info_frame, text="اسم الوزارة:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.ministry_name_var = tk.StringVar(value="وزارة الصحة العراقية")
        ttk.Entry(lab_info_frame, textvariable=self.ministry_name_var, width=50).grid(row=1, column=1, pady=5)
        
        # عنوان المختبر
        ttk.Label(lab_info_frame, text="عنوان المختبر:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        self.lab_address_var = tk.StringVar(value="ذي قار - العراق")
        ttk.Entry(lab_info_frame, textvariable=self.lab_address_var, width=50).grid(row=2, column=1, pady=5)
        
        # إعدادات الطباعة
        print_settings_frame = ttk.LabelFrame(reports_frame, text="إعدادات الطباعة", padding="10")
        print_settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # حجم الخط
        ttk.Label(print_settings_frame, text="حجم الخط:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.font_size_var = tk.StringVar(value="10")
        font_size_combo = ttk.Combobox(print_settings_frame, textvariable=self.font_size_var,
                                      values=['8', '9', '10', '11', '12'], state='readonly', width=10)
        font_size_combo.grid(row=0, column=1, pady=5)
        
        # نوع الخط
        ttk.Label(print_settings_frame, text="نوع الخط:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.font_type_var = tk.StringVar(value="Arial")
        font_type_combo = ttk.Combobox(print_settings_frame, textvariable=self.font_type_var,
                                      values=['Arial', 'Tahoma', 'Calibri'], state='readonly', width=15)
        font_type_combo.grid(row=1, column=1, pady=5)
        
        # أزرار الإعدادات
        settings_buttons = ttk.Frame(reports_frame)
        settings_buttons.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(settings_buttons, text="حفظ الإعدادات", 
                  command=self.save_report_settings).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(settings_buttons, text="استعادة الافتراضي", 
                  command=self.reset_report_settings).pack(side=tk.LEFT)
    
    def setup_backup_tab(self, parent):
        """إعداد تبويب النسخ الاحتياطي"""
        # إطار النسخ الاحتياطي
        backup_frame = ttk.LabelFrame(parent, text="إدارة النسخ الاحتياطي", padding="20")
        backup_frame.pack(fill=tk.X, pady=(0, 20))
        
        # معلومات قاعدة البيانات
        info_frame = ttk.Frame(backup_frame)
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.db_info_label = ttk.Label(info_frame, text="معلومات قاعدة البيانات سيتم تحميلها...")
        self.db_info_label.pack(anchor=tk.W)
        
        # أزرار النسخ الاحتياطي
        backup_buttons = ttk.Frame(backup_frame)
        backup_buttons.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(backup_buttons, text="إنشاء نسخة احتياطية", 
                  command=self.create_backup).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(backup_buttons, text="استعادة نسخة احتياطية", 
                  command=self.restore_backup).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(backup_buttons, text="تصدير البيانات", 
                  command=self.export_data).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(backup_buttons, text="استيراد البيانات", 
                  command=self.import_data).pack(side=tk.LEFT)
        
        # إطار سجل النسخ الاحتياطي
        log_frame = ttk.LabelFrame(parent, text="سجل العمليات", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # منطقة النص للسجل
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.config(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إضافة رسالة ترحيب
        self.add_log_entry("مرحباً بك في نظام إدارة النسخ الاحتياطي")
    
    def load_data(self):
        """تحميل البيانات الأولية"""
        self.load_sample_types()
        self.load_sending_entities()
        self.load_tests()
        self.load_database_info()
    
    def add_log_entry(self, message: str):
        """إضافة إدخال إلى السجل"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.window.update_idletasks()

    # دوال أنواع العينات
    def load_sample_types(self):
        """تحميل أنواع العينات"""
        # مسح الجدول الحالي
        for item in self.sample_types_tree.get_children():
            self.sample_types_tree.delete(item)

        # تحميل البيانات
        self.sample_types = self.db_manager.get_sample_types()

        for sample_type in self.sample_types:
            self.sample_types_tree.insert('', tk.END, values=(
                sample_type['id'],
                sample_type['name'],
                sample_type['created_at'][:10] if sample_type['created_at'] else ''
            ))

    def add_sample_type(self):
        """إضافة نوع عينة جديد"""
        name = self.sample_type_name_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم نوع العينة")
            return

        if self.db_manager.add_sample_type(name):
            messagebox.showinfo("نجح", "تم إضافة نوع العينة بنجاح")
            self.load_sample_types()
            self.clear_sample_type_form()

            # إشعار النوافذ الأخرى
            self.main_app.notify_observers('sample_type_added', {'name': name})
        else:
            messagebox.showerror("خطأ", "فشل في إضافة نوع العينة")

    def update_sample_type(self):
        """تحديث نوع عينة"""
        if not self.selected_sample_type_id:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع عينة للتعديل")
            return

        name = self.sample_type_name_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم نوع العينة")
            return

        if self.db_manager.update_sample_type(self.selected_sample_type_id, name):
            messagebox.showinfo("نجح", "تم تحديث نوع العينة بنجاح")
            self.load_sample_types()
            self.clear_sample_type_form()

            # إشعار النوافذ الأخرى
            self.main_app.notify_observers('sample_type_updated', {'id': self.selected_sample_type_id, 'name': name})
        else:
            messagebox.showerror("خطأ", "فشل في تحديث نوع العينة")

    def delete_sample_type(self):
        """حذف نوع عينة"""
        if not self.selected_sample_type_id:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع عينة للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف نوع العينة؟"):
            if self.db_manager.delete_sample_type(self.selected_sample_type_id):
                messagebox.showinfo("نجح", "تم حذف نوع العينة بنجاح")
                self.load_sample_types()
                self.clear_sample_type_form()

                # إشعار النوافذ الأخرى
                self.main_app.notify_observers('sample_type_deleted', {'id': self.selected_sample_type_id})
            else:
                messagebox.showerror("خطأ", "فشل في حذف نوع العينة")

    def clear_sample_type_form(self):
        """مسح نموذج نوع العينة"""
        self.sample_type_name_var.set("")
        self.selected_sample_type_id = None

    def on_sample_type_select(self, event):
        """عند اختيار نوع عينة"""
        selected_items = self.sample_types_tree.selection()
        if selected_items:
            item = selected_items[0]
            values = self.sample_types_tree.item(item)['values']

            self.selected_sample_type_id = values[0]
            self.sample_type_name_var.set(values[1])

    def on_sample_type_double_click(self, event):
        """النقر المزدوج على نوع عينة"""
        self.on_sample_type_select(event)

    # دوال جهات الإرسال
    def load_sending_entities(self):
        """تحميل جهات الإرسال"""
        # مسح الجدول الحالي
        for item in self.sending_entities_tree.get_children():
            self.sending_entities_tree.delete(item)

        # تحميل البيانات
        self.sending_entities = self.db_manager.get_sending_entities()

        for entity in self.sending_entities:
            self.sending_entities_tree.insert('', tk.END, values=(
                entity['id'],
                entity['name'],
                entity['created_at'][:10] if entity['created_at'] else ''
            ))

    def add_sending_entity(self):
        """إضافة جهة إرسال جديدة"""
        name = self.sending_entity_name_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم جهة الإرسال")
            return

        if self.db_manager.add_sending_entity(name):
            messagebox.showinfo("نجح", "تم إضافة جهة الإرسال بنجاح")
            self.load_sending_entities()
            self.clear_sending_entity_form()

            # إشعار النوافذ الأخرى
            self.main_app.notify_observers('sending_entity_added', {'name': name})
        else:
            messagebox.showerror("خطأ", "فشل في إضافة جهة الإرسال")

    def update_sending_entity(self):
        """تحديث جهة إرسال"""
        if not self.selected_sending_entity_id:
            messagebox.showwarning("تحذير", "يرجى اختيار جهة إرسال للتعديل")
            return

        name = self.sending_entity_name_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم جهة الإرسال")
            return

        if self.db_manager.update_sending_entity(self.selected_sending_entity_id, name):
            messagebox.showinfo("نجح", "تم تحديث جهة الإرسال بنجاح")
            self.load_sending_entities()
            self.clear_sending_entity_form()

            # إشعار النوافذ الأخرى
            self.main_app.notify_observers('sending_entity_updated', {'id': self.selected_sending_entity_id, 'name': name})
        else:
            messagebox.showerror("خطأ", "فشل في تحديث جهة الإرسال")

    def delete_sending_entity(self):
        """حذف جهة إرسال"""
        if not self.selected_sending_entity_id:
            messagebox.showwarning("تحذير", "يرجى اختيار جهة إرسال للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف جهة الإرسال؟"):
            if self.db_manager.delete_sending_entity(self.selected_sending_entity_id):
                messagebox.showinfo("نجح", "تم حذف جهة الإرسال بنجاح")
                self.load_sending_entities()
                self.clear_sending_entity_form()

                # إشعار النوافذ الأخرى
                self.main_app.notify_observers('sending_entity_deleted', {'id': self.selected_sending_entity_id})
            else:
                messagebox.showerror("خطأ", "فشل في حذف جهة الإرسال")

    def clear_sending_entity_form(self):
        """مسح نموذج جهة الإرسال"""
        self.sending_entity_name_var.set("")
        self.selected_sending_entity_id = None

    def on_sending_entity_select(self, event):
        """عند اختيار جهة إرسال"""
        selected_items = self.sending_entities_tree.selection()
        if selected_items:
            item = selected_items[0]
            values = self.sending_entities_tree.item(item)['values']

            self.selected_sending_entity_id = values[0]
            self.sending_entity_name_var.set(values[1])

    def on_sending_entity_double_click(self, event):
        """النقر المزدوج على جهة إرسال"""
        self.on_sending_entity_select(event)

    # دوال التحاليل
    def load_tests(self):
        """تحميل التحاليل"""
        # مسح الجدول الحالي
        for item in self.tests_tree.get_children():
            self.tests_tree.delete(item)

        # تحميل البيانات
        self.tests = self.db_manager.get_tests()

        for test in self.tests:
            self.tests_tree.insert('', tk.END, values=(
                test['id'],
                test['name'],
                test['description'] if test['description'] else '',
                test['created_at'][:10] if test['created_at'] else ''
            ))

    def add_test(self):
        """إضافة تحليل جديد"""
        name = self.test_name_var.get().strip()
        description = self.test_description_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم التحليل")
            return

        if self.db_manager.add_test(name, description):
            messagebox.showinfo("نجح", "تم إضافة التحليل بنجاح")
            self.load_tests()
            self.clear_test_form()

            # إشعار النوافذ الأخرى
            self.main_app.notify_observers('test_added', {'name': name, 'description': description})
        else:
            messagebox.showerror("خطأ", "فشل في إضافة التحليل")

    def update_test(self):
        """تحديث تحليل"""
        if not self.selected_test_id:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل للتعديل")
            return

        name = self.test_name_var.get().strip()
        description = self.test_description_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم التحليل")
            return

        if self.db_manager.update_test(self.selected_test_id, name, description):
            messagebox.showinfo("نجح", "تم تحديث التحليل بنجاح")
            self.load_tests()
            self.clear_test_form()

            # إشعار النوافذ الأخرى
            self.main_app.notify_observers('test_updated', {'id': self.selected_test_id, 'name': name, 'description': description})
        else:
            messagebox.showerror("خطأ", "فشل في تحديث التحليل")

    def delete_test(self):
        """حذف تحليل"""
        if not self.selected_test_id:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف التحليل؟"):
            if self.db_manager.delete_test(self.selected_test_id):
                messagebox.showinfo("نجح", "تم حذف التحليل بنجاح")
                self.load_tests()
                self.clear_test_form()

                # إشعار النوافذ الأخرى
                self.main_app.notify_observers('test_deleted', {'id': self.selected_test_id})
            else:
                messagebox.showerror("خطأ", "فشل في حذف التحليل")

    def clear_test_form(self):
        """مسح نموذج التحليل"""
        self.test_name_var.set("")
        self.test_description_var.set("")
        self.selected_test_id = None

    def on_test_select(self, event):
        """عند اختيار تحليل"""
        selected_items = self.tests_tree.selection()
        if selected_items:
            item = selected_items[0]
            values = self.tests_tree.item(item)['values']

            self.selected_test_id = values[0]
            self.test_name_var.set(values[1])
            self.test_description_var.set(values[2])

    def on_test_double_click(self, event):
        """النقر المزدوج على تحليل"""
        self.on_test_select(event)

    # دوال إعدادات التقارير
    def save_report_settings(self):
        """حفظ إعدادات التقارير"""
        try:
            settings = {
                'lab_name': self.lab_name_var.get(),
                'ministry_name': self.ministry_name_var.get(),
                'lab_address': self.lab_address_var.get(),
                'font_size': self.font_size_var.get(),
                'font_type': self.font_type_var.get()
            }

            # حفظ الإعدادات في ملف JSON
            with open('report_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح", "تم حفظ إعدادات التقارير بنجاح")
            self.add_log_entry("تم حفظ إعدادات التقارير")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
            self.add_log_entry(f"خطأ في حفظ الإعدادات: {str(e)}")

    def reset_report_settings(self):
        """استعادة الإعدادات الافتراضية"""
        self.lab_name_var.set("مختبر الصحة العامة المركزي - ذي قار")
        self.ministry_name_var.set("وزارة الصحة العراقية")
        self.lab_address_var.set("ذي قار - العراق")
        self.font_size_var.set("10")
        self.font_type_var.set("Arial")

        messagebox.showinfo("تم", "تم استعادة الإعدادات الافتراضية")
        self.add_log_entry("تم استعادة الإعدادات الافتراضية")

    # دوال النسخ الاحتياطي
    def load_database_info(self):
        """تحميل معلومات قاعدة البيانات"""
        try:
            # حساب إحصائيات قاعدة البيانات
            patients_count = self.db_manager.execute_query("SELECT COUNT(*) as count FROM patients")[0]['count']
            tests_count = self.db_manager.execute_query("SELECT COUNT(*) as count FROM tests")[0]['count']
            results_count = self.db_manager.execute_query("SELECT COUNT(*) as count FROM results")[0]['count']

            # حجم ملف قاعدة البيانات
            db_size = 0
            if os.path.exists(self.db_manager.db_path):
                db_size = os.path.getsize(self.db_manager.db_path) / 1024  # بالكيلوبايت

            info_text = f"""
معلومات قاعدة البيانات:
• عدد المرضى: {patients_count}
• عدد التحاليل: {tests_count}
• عدد النتائج: {results_count}
• حجم قاعدة البيانات: {db_size:.2f} KB
• مسار قاعدة البيانات: {self.db_manager.db_path}
            """

            self.db_info_label.config(text=info_text)

        except Exception as e:
            self.db_info_label.config(text=f"خطأ في تحميل معلومات قاعدة البيانات: {str(e)}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            # اختيار مكان حفظ النسخة الاحتياطية
            backup_path = filedialog.asksaveasfilename(
                title="حفظ النسخة الاحتياطية",
                defaultextension=".db",
                filetypes=[("Database files", "*.db"), ("All files", "*.*")]
            )

            if backup_path:
                # نسخ ملف قاعدة البيانات
                import shutil
                shutil.copy2(self.db_manager.db_path, backup_path)

                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح في:\n{backup_path}")
                self.add_log_entry(f"تم إنشاء نسخة احتياطية: {backup_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
            self.add_log_entry(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            # تحذير المستخدم
            if not messagebox.askyesno("تحذير",
                                     "سيتم استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية.\n"
                                     "هل أنت متأكد من المتابعة؟"):
                return

            # اختيار ملف النسخة الاحتياطية
            backup_path = filedialog.askopenfilename(
                title="اختيار النسخة الاحتياطية",
                filetypes=[("Database files", "*.db"), ("All files", "*.*")]
            )

            if backup_path:
                # نسخ النسخة الاحتياطية مكان قاعدة البيانات الحالية
                import shutil
                shutil.copy2(backup_path, self.db_manager.db_path)

                messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح")
                self.add_log_entry(f"تم استعادة نسخة احتياطية من: {backup_path}")

                # إعادة تحميل البيانات
                self.load_data()

                # إشعار النوافذ الأخرى
                self.main_app.notify_observers('database_restored', {})

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية: {str(e)}")
            self.add_log_entry(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")

    def export_data(self):
        """تصدير البيانات إلى Excel"""
        try:
            import pandas as pd

            # اختيار مكان حفظ الملف
            export_path = filedialog.asksaveasfilename(
                title="تصدير البيانات",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )

            if export_path:
                # إنشاء كاتب Excel
                with pd.ExcelWriter(export_path, engine='openpyxl') as writer:

                    # تصدير المرضى
                    patients_query = """
                        SELECT p.*, st.name as sample_type, se.name as sending_entity
                        FROM patients p
                        JOIN sample_types st ON p.sample_type_id = st.id
                        JOIN sending_entities se ON p.sending_entity_id = se.id
                    """
                    patients_data = self.db_manager.execute_query(patients_query)
                    if patients_data:
                        patients_df = pd.DataFrame(patients_data)
                        patients_df.to_excel(writer, sheet_name='المرضى', index=False)

                    # تصدير النتائج
                    results_query = """
                        SELECT r.*, p.name as patient_name, p.national_number, t.name as test_name
                        FROM results r
                        JOIN patients p ON r.patient_id = p.id
                        JOIN tests t ON r.test_id = t.id
                    """
                    results_data = self.db_manager.execute_query(results_query)
                    if results_data:
                        results_df = pd.DataFrame(results_data)
                        results_df.to_excel(writer, sheet_name='النتائج', index=False)

                    # تصدير أنواع العينات
                    sample_types_df = pd.DataFrame(self.sample_types)
                    sample_types_df.to_excel(writer, sheet_name='أنواع العينات', index=False)

                    # تصدير جهات الإرسال
                    sending_entities_df = pd.DataFrame(self.sending_entities)
                    sending_entities_df.to_excel(writer, sheet_name='جهات الإرسال', index=False)

                    # تصدير التحاليل
                    tests_df = pd.DataFrame(self.tests)
                    tests_df.to_excel(writer, sheet_name='التحاليل', index=False)

                messagebox.showinfo("نجح", f"تم تصدير البيانات بنجاح إلى:\n{export_path}")
                self.add_log_entry(f"تم تصدير البيانات إلى: {export_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير البيانات: {str(e)}")
            self.add_log_entry(f"خطأ في تصدير البيانات: {str(e)}")

    def import_data(self):
        """استيراد البيانات من Excel"""
        try:
            import pandas as pd

            # تحذير المستخدم
            if not messagebox.askyesno("تحذير",
                                     "سيتم إضافة البيانات المستوردة إلى قاعدة البيانات الحالية.\n"
                                     "هل أنت متأكد من المتابعة؟"):
                return

            # اختيار ملف البيانات
            import_path = filedialog.askopenfilename(
                title="اختيار ملف البيانات",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )

            if import_path:
                # قراءة الملف
                excel_file = pd.ExcelFile(import_path)
                imported_count = 0

                # استيراد أنواع العينات
                if 'أنواع العينات' in excel_file.sheet_names:
                    sample_types_df = pd.read_excel(import_path, sheet_name='أنواع العينات')
                    for _, row in sample_types_df.iterrows():
                        if 'name' in row and not pd.isna(row['name']):
                            self.db_manager.add_sample_type(str(row['name']))
                            imported_count += 1

                # استيراد جهات الإرسال
                if 'جهات الإرسال' in excel_file.sheet_names:
                    entities_df = pd.read_excel(import_path, sheet_name='جهات الإرسال')
                    for _, row in entities_df.iterrows():
                        if 'name' in row and not pd.isna(row['name']):
                            self.db_manager.add_sending_entity(str(row['name']))
                            imported_count += 1

                # استيراد التحاليل
                if 'التحاليل' in excel_file.sheet_names:
                    tests_df = pd.read_excel(import_path, sheet_name='التحاليل')
                    for _, row in tests_df.iterrows():
                        if 'name' in row and not pd.isna(row['name']):
                            description = str(row.get('description', '')) if not pd.isna(row.get('description')) else ''
                            self.db_manager.add_test(str(row['name']), description)
                            imported_count += 1

                messagebox.showinfo("نجح", f"تم استيراد {imported_count} عنصر بنجاح")
                self.add_log_entry(f"تم استيراد {imported_count} عنصر من: {import_path}")

                # إعادة تحميل البيانات
                self.load_data()

                # إشعار النوافذ الأخرى
                self.main_app.notify_observers('data_imported', {'count': imported_count})

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في استيراد البيانات: {str(e)}")
            self.add_log_entry(f"خطأ في استيراد البيانات: {str(e)}")

    # دوال التزامن
    def on_data_changed(self, event_type: str, data: dict = None):
        """استقبال إشعارات التحديث من النوافذ الأخرى"""
        # لا نحتاج لتحديث شيء هنا لأن هذه النافذة تدير البيانات المرجعية
        pass

    def __del__(self):
        """تنظيف الموارد عند إغلاق النافذة"""
        try:
            if hasattr(self, 'main_app') and self in self.main_app.observers:
                self.main_app.observers.remove(self)
        except:
            pass
