# مختبر الصحة العامة المركزي - ذي قار
## Central Public Health Laboratory - Dhi Qar

نظام إدارة شامل لمختبر الصحة العامة المركزي في ذي قار، العراق.

## المميزات الرئيسية

### 🔬 إدارة العينات والمرضى
- إدخال بيانات المرضى مع التحقق من صحة البيانات
- إنتاج وطباعة الباركود تلقائياً
- استيراد البيانات من ملفات Excel
- البحث والفلترة المتقدمة

### 📊 إدارة العمل والوجبات
- تنظيم العينات في وجبات عمل
- تعيين العاملين للتحاليل المختلفة
- طباعة قوائم العمل اليومية
- تتبع تقدم العمل

### 🧪 إدارة النتائج
- إدخال النتائج بطرق متعددة (فردي ومتعدد)
- أنواع النتائج: سلبي، إيجابي، إعادة فحص، إعادة سحب، مُرسل، غير محدد
- تتبع حالة التحاليل المعلقة
- طباعة التقارير الفردية

### 📈 التقارير والإحصائيات
- تقارير شاملة مع فلاتر متقدمة
- رسوم بيانية تفاعلية
- قراءة الباركود للبحث السريع
- تصدير التقارير إلى PDF و Excel

### ⚙️ الإعدادات والإدارة
- إدارة أنواع العينات وجهات الإرسال والتحاليل
- إعدادات التقارير والطباعة
- النسخ الاحتياطي واستعادة البيانات
- تصدير واستيراد البيانات

## متطلبات النظام

### البرمجيات المطلوبة
- Python 3.8 أو أحدث
- نظام التشغيل: Windows 10/11

### المكتبات المطلوبة
```
python-barcode==0.15.1
Pillow==10.0.1
reportlab==4.0.4
openpyxl==3.1.2
pandas==2.1.3
ttkthemes==3.2.2
opencv-python==********
pyzbar==0.1.9
matplotlib==3.10.3
```

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd lab-management-system
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python main.py
```

## هيكل المشروع

```
lab-management-system/
├── main.py                    # الملف الرئيسي للتطبيق
├── database.py               # إدارة قاعدة البيانات
├── data_entry_window.py      # نافذة إدخال البيانات
├── work_window.py            # نافذة العمل
├── results_window.py         # نافذة النتائج
├── reports_window.py         # نافذة التقارير
├── settings_window.py        # نافذة الإعدادات
├── barcode_generator.py      # مولد الباركود
├── work_list_printer.py      # طابع قوائم العمل
├── results_printer.py        # طابع النتائج
├── reports_printer.py        # طابع التقارير
├── database_schema.sql       # مخطط قاعدة البيانات
├── requirements.txt          # متطلبات المشروع
└── README.md                # هذا الملف
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:

### الجداول الرئيسية
- **patients**: بيانات المرضى والعينات
- **tests**: التحاليل المتاحة
- **results**: نتائج التحاليل
- **batches**: وجبات العمل
- **sample_types**: أنواع العينات
- **sending_entities**: جهات الإرسال

### العلاقات
- علاقة واحد إلى متعدد بين المرضى والنتائج
- علاقة متعدد إلى متعدد بين المرضى والتحاليل
- علاقة واحد إلى متعدد بين الوجبات والعينات

## الاستخدام

### 1. إدخال البيانات
1. افتح نافذة "إدخال البيانات"
2. أدخل بيانات المريض (الحقول الإلزامية مميزة بـ *)
3. اختر التحاليل المطلوبة
4. احفظ البيانات - سيتم إنتاج الباركود تلقائياً

### 2. إدارة العمل
1. افتح نافذة "العمل"
2. حدد الفترة الزمنية للعينات
3. أضف العينات إلى الوجبة
4. عيّن العاملين للتحاليل
5. احفظ الوجبة واطبع قائمة العمل

### 3. إدخال النتائج
1. افتح نافذة "النتائج"
2. اختر التحليل المطلوب
3. أدخل النتيجة والملاحظات
4. احفظ النتيجة

### 4. التقارير
1. افتح نافذة "التقارير والإحصائيات"
2. طبق الفلاتر المطلوبة
3. اعرض التقرير أو اطبعه أو صدّره

## المميزات المتقدمة

### طباعة الباركود
- التعرف التلقائي على طابعة الباركود
- طباعة فورية عند إدخال العينة
- دعم طباعة متعددة

### قراءة الباركود
- بحث سريع بالباركود
- دعم قارئات الباركود المختلفة
- عرض حالة العينة فوراً

### التزامن بين النوافذ
- تحديث فوري للبيانات في جميع النوافذ
- نظام إشعارات داخلي
- ضمان تطابق البيانات

### النسخ الاحتياطي
- نسخ احتياطي كامل لقاعدة البيانات
- استعادة البيانات
- تصدير واستيراد البيانات

## الدعم الفني

### المشاكل الشائعة

**مشكلة: لا تظهر الخطوط العربية بشكل صحيح**
- تأكد من وجود خطوط عربية في النظام (Arial, Tahoma)
- أعد تشغيل التطبيق

**مشكلة: فشل في طباعة الباركود**
- تأكد من تثبيت طابعة
- تحقق من إعدادات الطابعة الافتراضية

**مشكلة: خطأ في قاعدة البيانات**
- تأكد من صلاحيات الكتابة في مجلد التطبيق
- أنشئ نسخة احتياطية واستعدها

### التطوير والمساهمة

لتطوير المشروع أو إضافة مميزات جديدة:

1. انسخ المشروع (Fork)
2. أنشئ فرع جديد للميزة
3. اختبر التغييرات
4. أرسل طلب دمج (Pull Request)

## الترخيص

هذا المشروع مطور خصيصاً لمختبر الصحة العامة المركزي في ذي قار.
جميع الحقوق محفوظة لوزارة الصحة العراقية.

## معلومات الاتصال

للدعم الفني أو الاستفسارات:
- البريد الإلكتروني: [email]
- الهاتف: [phone]
- العنوان: مختبر الصحة العامة المركزي - ذي قار، العراق

---

تم تطوير هذا النظام بواسطة فريق التطوير المتخصص لخدمة القطاع الصحي في العراق.
