# -*- coding: utf-8 -*-
"""
طابع قائمة العمل - مختبر الصحة العامة المركزي ذي قار
Work List Printer - Central Public Health Laboratory
"""

import os
import tempfile
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

class WorkListPrinter:
    def __init__(self):
        """تهيئة طابع قائمة العمل"""
        self.temp_dir = tempfile.gettempdir()
        self.setup_fonts()
        self.setup_styles()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي
            arabic_fonts = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
                "C:/Windows/Fonts/calibri.ttf"
            ]
            
            for font_path in arabic_fonts:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    break
            else:
                # استخدام خط افتراضي إذا لم توجد خطوط عربية
                self.arabic_font = 'Helvetica'
                return
            
            self.arabic_font = 'Arabic'
            
        except Exception as e:
            print(f"خطأ في تحميل الخط العربي: {e}")
            self.arabic_font = 'Helvetica'
    
    def setup_styles(self):
        """إعداد أنماط النص"""
        self.styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Title'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.black
        )
        
        # نمط العنوان الفرعي
        self.subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=self.styles['Heading2'],
            fontName=self.arabic_font,
            fontSize=14,
            alignment=TA_CENTER,
            spaceAfter=15,
            textColor=colors.darkblue
        )
        
        # نمط النص العادي
        self.normal_style = ParagraphStyle(
            'CustomNormal',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_RIGHT,
            spaceAfter=6
        )
        
        # نمط النص المتوسط
        self.center_style = ParagraphStyle(
            'CustomCenter',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_CENTER
        )
    
    def print_work_list(self, batch_data: dict):
        """طباعة قائمة العمل"""
        try:
            # إنشاء ملف PDF مؤقت
            pdf_file = os.path.join(self.temp_dir, f"work_list_{batch_data['batch_number']}.pdf")
            
            # إنشاء المستند
            doc = SimpleDocTemplate(
                pdf_file,
                pagesize=A4,
                rightMargin=0.5*inch,
                leftMargin=0.5*inch,
                topMargin=0.5*inch,
                bottomMargin=0.5*inch
            )
            
            # بناء محتوى المستند
            story = []
            
            # إضافة الرأس
            self.add_header(story, batch_data)
            
            # إضافة معلومات الوجبة
            self.add_batch_info(story, batch_data)
            
            # إضافة قائمة العينات
            self.add_samples_table(story, batch_data)
            
            # إضافة قائمة العاملين
            self.add_workers_table(story, batch_data)
            
            # إضافة التذييل
            self.add_footer(story)
            
            # بناء المستند
            doc.build(story)
            
            # فتح الملف للطباعة
            os.startfile(pdf_file, "print")
            
            return pdf_file
            
        except Exception as e:
            print(f"خطأ في طباعة قائمة العمل: {e}")
            raise e
    
    def add_header(self, story, batch_data):
        """إضافة رأس المستند"""
        # شعار وزارة الصحة (إذا توفر)
        try:
            logo_path = "ministry_logo.png"
            if os.path.exists(logo_path):
                logo = Image(logo_path, width=1*inch, height=1*inch)
                story.append(logo)
                story.append(Spacer(1, 10))
        except:
            pass
        
        # عنوان وزارة الصحة
        ministry_title = Paragraph("وزارة الصحة العراقية", self.title_style)
        story.append(ministry_title)
        
        # عنوان المختبر
        lab_title = Paragraph("مختبر الصحة العامة المركزي - ذي قار", self.subtitle_style)
        story.append(lab_title)
        
        # عنوان قائمة العمل
        work_list_title = Paragraph("قائمة العمل اليومية", self.subtitle_style)
        story.append(work_list_title)
        
        story.append(Spacer(1, 20))
    
    def add_batch_info(self, story, batch_data):
        """إضافة معلومات الوجبة"""
        # جدول معلومات الوجبة
        batch_info_data = [
            ["رقم الوجبة:", batch_data['batch_number'], "تاريخ العمل:", batch_data['work_date']],
            ["فترة العينات:", f"من {batch_data['start_date']} إلى {batch_data['end_date']}", 
             "عدد العينات:", str(len(batch_data['samples']))]
        ]
        
        batch_info_table = Table(batch_info_data, colWidths=[1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch])
        batch_info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
        ]))
        
        story.append(batch_info_table)
        story.append(Spacer(1, 20))
    
    def add_samples_table(self, story, batch_data):
        """إضافة جدول العينات"""
        # عنوان الجدول
        samples_title = Paragraph("قائمة العينات", self.subtitle_style)
        story.append(samples_title)
        story.append(Spacer(1, 10))
        
        # رأس الجدول
        samples_data = [["ت", "الرقم الوطني", "اسم المريض", "نوع العينة", "التحاليل المطلوبة"]]
        
        # إضافة بيانات العينات
        for i, sample in enumerate(batch_data['samples'], 1):
            samples_data.append([
                str(i),
                sample['national_number'],
                sample['name'],
                sample['sample_type'],
                sample['tests']
            ])
        
        # إنشاء الجدول
        samples_table = Table(samples_data, colWidths=[0.5*inch, 1*inch, 2*inch, 1.5*inch, 2*inch])
        samples_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))
        
        story.append(samples_table)
        story.append(Spacer(1, 20))
    
    def add_workers_table(self, story, batch_data):
        """إضافة جدول العاملين"""
        if not batch_data['workers']:
            return
        
        # عنوان الجدول
        workers_title = Paragraph("العاملون في التحاليل", self.subtitle_style)
        story.append(workers_title)
        story.append(Spacer(1, 10))
        
        # رأس الجدول
        workers_data = [["التحليل", "اسم العامل", "عدد العينات", "التوقيع"]]
        
        # إضافة بيانات العاملين
        for worker in batch_data['workers']:
            workers_data.append([
                worker['test'],
                worker['worker'],
                str(worker['count']),
                ""  # مساحة للتوقيع
            ])
        
        # إنشاء الجدول
        workers_table = Table(workers_data, colWidths=[2*inch, 2*inch, 1*inch, 2*inch])
        workers_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))
        
        story.append(workers_table)
        story.append(Spacer(1, 20))
    
    def add_footer(self, story):
        """إضافة تذييل المستند"""
        # تاريخ ووقت الطباعة
        print_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        footer_text = f"تم الطباعة في: {print_time}"
        footer = Paragraph(footer_text, self.center_style)
        
        story.append(Spacer(1, 30))
        story.append(footer)
        
        # مساحة للتوقيعات
        signature_text = """
        <br/><br/>
        توقيع المسؤول: __________________ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; التاريخ: __________________
        <br/><br/>
        توقيع المدير: __________________ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; التاريخ: __________________
        """
        
        signature = Paragraph(signature_text, self.normal_style)
        story.append(signature)
