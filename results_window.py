# -*- coding: utf-8 -*-
"""
نافذة النتائج - مختبر الصحة العامة المركزي ذي قار
Results Window - Central Public Health Laboratory
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from typing import Dict, List, Any

class ResultsWindow:
    def __init__(self, parent, db_manager, main_app):
        """تهيئة نافذة النتائج"""
        self.parent = parent
        self.db_manager = db_manager
        self.main_app = main_app
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_variables()
        self.setup_interface()
        self.load_data()
        
        # تسجيل كمراقب للتحديثات
        self.main_app.add_observer(self)
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("🧪 النتائج - مختبر الصحة العامة المركزي")
        self.window.geometry("1400x900")
        self.window.resizable(True, True)

        # تعيين خلفية عصرية
        self.window.configure(bg='#f8f9fa')

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
    
    def setup_variables(self):
        """إعداد المتغيرات"""
        # متغيرات البحث والفلترة
        self.search_var = tk.StringVar()
        self.filter_test_var = tk.StringVar()
        self.filter_result_var = tk.StringVar()
        self.filter_date_from_var = tk.StringVar()
        self.filter_date_to_var = tk.StringVar()
        
        # متغيرات إدخال النتائج
        self.selected_patient_id = None
        self.selected_test_id = None
        self.result_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # قوائم البيانات
        self.pending_tests = []
        self.tests = []
        self.result_options = ['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND']
        
        # تعيين التواريخ الافتراضية
        today = datetime.now().strftime('%Y-%m-%d')
        self.filter_date_from_var.set(today)
        self.filter_date_to_var.set(today)
    
    def setup_interface(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي مع شريط تمرير
        canvas = tk.Canvas(self.window)
        scrollbar = ttk.Scrollbar(self.window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # تخطيط Canvas
        canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y")

        # إطار البحث والفلترة
        filter_frame = ttk.LabelFrame(scrollable_frame, text="🔍 البحث والفلترة", padding="10")
        filter_frame.pack(fill=tk.X, pady=(0, 10))

        self.setup_filter_section(filter_frame)

        # إطار النتائج المعلقة
        pending_frame = ttk.LabelFrame(scrollable_frame, text="⏳ التحاليل المعلقة", padding="10")
        pending_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.setup_pending_tests_section(pending_frame)

        # إطار إدخال النتائج
        input_frame = ttk.LabelFrame(scrollable_frame, text="📝 إدخال النتائج", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))

        self.setup_result_input_section(input_frame)

        # إطار العمليات
        operations_frame = ttk.LabelFrame(scrollable_frame, text="⚡ العمليات", padding="10")
        operations_frame.pack(fill=tk.X, pady=(0, 10))

        self.setup_operations_section(operations_frame)

        # ربط عجلة الماوس بالتمرير
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    def setup_filter_section(self, parent):
        """إعداد قسم البحث والفلترة"""
        # الصف الأول - البحث
        search_frame = ttk.Frame(parent)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="البحث:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.grid(row=0, column=1, padx=(0, 20))
        search_entry.bind('<KeyRelease>', self.on_search_changed)
        
        ttk.Button(search_frame, text="بحث", command=self.search_tests).grid(row=0, column=2, padx=5)
        ttk.Button(search_frame, text="مسح", command=self.clear_search).grid(row=0, column=3, padx=5)
        
        # الصف الثاني - الفلاتر
        filter_row = ttk.Frame(parent)
        filter_row.pack(fill=tk.X, pady=(0, 10))
        
        # فلتر التحليل
        ttk.Label(filter_row, text="التحليل:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.test_filter_combo = ttk.Combobox(filter_row, textvariable=self.filter_test_var, 
                                             state='readonly', width=15)
        self.test_filter_combo.grid(row=0, column=1, padx=(0, 20))
        
        # فلتر النتيجة
        ttk.Label(filter_row, text="النتيجة:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.result_filter_combo = ttk.Combobox(filter_row, textvariable=self.filter_result_var,
                                               values=[''] + self.result_options, state='readonly', width=12)
        self.result_filter_combo.grid(row=0, column=3, padx=(0, 20))
        
        # فلتر التاريخ
        ttk.Label(filter_row, text="من تاريخ:").grid(row=0, column=4, sticky=tk.W, padx=(0, 5))
        date_from_entry = ttk.Entry(filter_row, textvariable=self.filter_date_from_var, width=12)
        date_from_entry.grid(row=0, column=5, padx=(0, 10))
        
        ttk.Label(filter_row, text="إلى تاريخ:").grid(row=0, column=6, sticky=tk.W, padx=(0, 5))
        date_to_entry = ttk.Entry(filter_row, textvariable=self.filter_date_to_var, width=12)
        date_to_entry.grid(row=0, column=7, padx=(0, 20))
        
        # زر تطبيق الفلاتر
        ttk.Button(filter_row, text="تطبيق الفلاتر", 
                  command=self.apply_filters).grid(row=0, column=8, padx=5)
        ttk.Button(filter_row, text="مسح الفلاتر", 
                  command=self.clear_filters).grid(row=0, column=9, padx=5)
    
    def setup_pending_tests_section(self, parent):
        """إعداد قسم التحاليل المعلقة"""
        # إطار الجدول
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الجدول
        columns = ('الرقم الوطني', 'اسم المريض', 'التحليل', 'نوع العينة', 
                  'تاريخ السحب', 'تاريخ الاستلام', 'النتيجة', 'تاريخ النتيجة', 'ملاحظات')
        
        self.pending_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        column_widths = [100, 150, 120, 100, 100, 100, 80, 100, 150]
        for i, col in enumerate(columns):
            self.pending_tree.heading(col, text=col)
            self.pending_tree.column(col, width=column_widths[i], anchor=tk.CENTER)
        
        # شريط التمرير
        tree_scrollbar_y = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.pending_tree.yview)
        tree_scrollbar_x = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.pending_tree.xview)
        self.pending_tree.config(yscrollcommand=tree_scrollbar_y.set, xscrollcommand=tree_scrollbar_x.set)
        
        # تخطيط الجدول
        self.pending_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # ربط الأحداث
        self.pending_tree.bind('<Button-1>', self.on_test_select)
        self.pending_tree.bind('<Double-1>', self.on_test_double_click)
    
    def setup_result_input_section(self, parent):
        """إعداد قسم إدخال النتائج"""
        # معلومات التحليل المحدد
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.selected_info_label = ttk.Label(info_frame, text="لم يتم اختيار تحليل", 
                                           font=('Arial', 10, 'bold'))
        self.selected_info_label.pack(anchor=tk.W)
        
        # إدخال النتيجة
        result_frame = ttk.Frame(parent)
        result_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(result_frame, text="النتيجة:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.result_combo = ttk.Combobox(result_frame, textvariable=self.result_var,
                                        values=self.result_options, state='readonly', width=15)
        self.result_combo.grid(row=0, column=1, padx=(0, 20))
        
        # ملاحظات
        ttk.Label(result_frame, text="ملاحظات:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        notes_entry = ttk.Entry(result_frame, textvariable=self.notes_var, width=40)
        notes_entry.grid(row=0, column=3, padx=(0, 20))
        
        # أزرار سريعة للنتائج الشائعة
        quick_buttons = ttk.Frame(parent)
        quick_buttons.pack(fill=tk.X)
        
        ttk.Button(quick_buttons, text="سلبي", 
                  command=lambda: self.set_quick_result('Negative')).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_buttons, text="إيجابي", 
                  command=lambda: self.set_quick_result('Positive')).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_buttons, text="إعادة فحص", 
                  command=lambda: self.set_quick_result('Retest')).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_buttons, text="إعادة سحب", 
                  command=lambda: self.set_quick_result('Recollection')).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_buttons, text="مُرسل", 
                  command=lambda: self.set_quick_result('Sent')).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_buttons, text="غير محدد", 
                  command=lambda: self.set_quick_result('TND')).pack(side=tk.LEFT)
    
    def setup_operations_section(self, parent):
        """إعداد قسم العمليات"""
        # أزرار العمليات الرئيسية
        main_ops = ttk.Frame(parent)
        main_ops.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(main_ops, text="حفظ النتيجة", 
                  command=self.save_result, 
                  style='Accent.TButton').pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(main_ops, text="حذف النتيجة", 
                  command=self.delete_result).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(main_ops, text="إدخال نتائج متعددة", 
                  command=self.bulk_result_entry).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(main_ops, text="تحديث", 
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 10))
        
        # عمليات إضافية
        additional_ops = ttk.Frame(parent)
        additional_ops.pack(fill=tk.X)
        
        ttk.Button(additional_ops, text="تصدير النتائج", 
                  command=self.export_results).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(additional_ops, text="طباعة النتائج", 
                  command=self.print_results).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(additional_ops, text="إحصائيات سريعة",
                  command=self.show_quick_stats).pack(side=tk.LEFT)

    def load_data(self):
        """تحميل البيانات الأولية"""
        # تحميل التحاليل
        self.tests = self.db_manager.get_tests()
        test_names = [''] + [test['name'] for test in self.tests]
        self.test_filter_combo['values'] = test_names

        # تحميل التحاليل المعلقة
        self.load_pending_tests()

    def load_pending_tests(self):
        """تحميل التحاليل المعلقة"""
        # مسح الجدول الحالي
        for item in self.pending_tree.get_children():
            self.pending_tree.delete(item)

        # استعلام التحاليل المعلقة والمكتملة
        query = """
            SELECT
                p.id as patient_id,
                p.national_number,
                p.name as patient_name,
                t.id as test_id,
                t.name as test_name,
                st.name as sample_type,
                p.sample_collection_date,
                p.sample_received_date,
                r.result,
                r.result_date,
                r.notes
            FROM patients p
            JOIN patient_tests pt ON p.id = pt.patient_id
            JOIN tests t ON pt.test_id = t.id
            JOIN sample_types st ON p.sample_type_id = st.id
            LEFT JOIN results r ON p.id = r.patient_id AND t.id = r.test_id
            ORDER BY p.sample_received_date DESC, p.national_number, t.name
        """

        self.pending_tests = self.db_manager.execute_query(query)

        # عرض البيانات في الجدول
        for test in self.pending_tests:
            result_text = test['result'] if test['result'] else 'معلق'
            result_date = test['result_date'] if test['result_date'] else ''
            notes = test['notes'] if test['notes'] else ''

            # تلوين الصفوف حسب حالة النتيجة
            tags = []
            if not test['result']:
                tags = ['pending']
            elif test['result'] == 'Positive':
                tags = ['positive']
            elif test['result'] == 'Negative':
                tags = ['negative']
            elif test['result'] in ['Retest', 'Recollection']:
                tags = ['retest']

            self.pending_tree.insert('', tk.END, values=(
                test['national_number'],
                test['patient_name'],
                test['test_name'],
                test['sample_type'],
                test['sample_collection_date'],
                test['sample_received_date'],
                result_text,
                result_date,
                notes
            ), tags=tags)

        # تطبيق الألوان
        self.pending_tree.tag_configure('pending', background='#ffeeee')
        self.pending_tree.tag_configure('positive', background='#ffcccc')
        self.pending_tree.tag_configure('negative', background='#ccffcc')
        self.pending_tree.tag_configure('retest', background='#ffffcc')

        # تحديث شريط الحالة
        pending_count = len([t for t in self.pending_tests if not t['result']])
        total_count = len(self.pending_tests)
        self.main_app.update_status(f"إجمالي التحاليل: {total_count} | المعلقة: {pending_count}")

    # دوال البحث والفلترة
    def search_tests(self):
        """البحث في التحاليل"""
        search_term = self.search_var.get().strip()
        if not search_term:
            self.load_pending_tests()
            return

        # تطبيق البحث
        self.apply_search_and_filters()

    def on_search_changed(self, event):
        """عند تغيير نص البحث"""
        # البحث التلقائي عند الكتابة
        self.window.after(500, self.search_tests)

    def clear_search(self):
        """مسح البحث"""
        self.search_var.set("")
        self.load_pending_tests()

    def apply_filters(self):
        """تطبيق الفلاتر"""
        self.apply_search_and_filters()

    def clear_filters(self):
        """مسح الفلاتر"""
        self.filter_test_var.set("")
        self.filter_result_var.set("")
        today = datetime.now().strftime('%Y-%m-%d')
        self.filter_date_from_var.set(today)
        self.filter_date_to_var.set(today)
        self.load_pending_tests()

    def apply_search_and_filters(self):
        """تطبيق البحث والفلاتر معاً"""
        # مسح الجدول الحالي
        for item in self.pending_tree.get_children():
            self.pending_tree.delete(item)

        search_term = self.search_var.get().strip().lower()
        filter_test = self.filter_test_var.get()
        filter_result = self.filter_result_var.get()
        filter_date_from = self.filter_date_from_var.get()
        filter_date_to = self.filter_date_to_var.get()

        # تطبيق الفلاتر على البيانات
        filtered_tests = []

        for test in self.pending_tests:
            # فلتر البحث
            if search_term:
                searchable_text = f"{test['national_number']} {test['patient_name']} {test['test_name']}".lower()
                if search_term not in searchable_text:
                    continue

            # فلتر التحليل
            if filter_test and test['test_name'] != filter_test:
                continue

            # فلتر النتيجة
            if filter_result:
                if filter_result == 'معلق' and test['result']:
                    continue
                elif filter_result != 'معلق' and test['result'] != filter_result:
                    continue

            # فلتر التاريخ
            try:
                if filter_date_from and filter_date_to:
                    test_date = datetime.strptime(test['sample_received_date'][:10], '%Y-%m-%d').date()
                    from_date = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
                    to_date = datetime.strptime(filter_date_to, '%Y-%m-%d').date()

                    if not (from_date <= test_date <= to_date):
                        continue
            except ValueError:
                pass  # تجاهل أخطاء التاريخ

            filtered_tests.append(test)

        # عرض النتائج المفلترة
        for test in filtered_tests:
            result_text = test['result'] if test['result'] else 'معلق'
            result_date = test['result_date'] if test['result_date'] else ''
            notes = test['notes'] if test['notes'] else ''

            # تلوين الصفوف
            tags = []
            if not test['result']:
                tags = ['pending']
            elif test['result'] == 'Positive':
                tags = ['positive']
            elif test['result'] == 'Negative':
                tags = ['negative']
            elif test['result'] in ['Retest', 'Recollection']:
                tags = ['retest']

            self.pending_tree.insert('', tk.END, values=(
                test['national_number'],
                test['patient_name'],
                test['test_name'],
                test['sample_type'],
                test['sample_collection_date'],
                test['sample_received_date'],
                result_text,
                result_date,
                notes
            ), tags=tags)

        # تحديث شريط الحالة
        self.main_app.update_status(f"تم العثور على {len(filtered_tests)} نتيجة")

    # دوال إدارة النتائج
    def on_test_select(self, event):
        """عند اختيار تحليل من الجدول"""
        selected_items = self.pending_tree.selection()
        if selected_items:
            item = selected_items[0]
            values = self.pending_tree.item(item)['values']

            # البحث عن التحليل في البيانات
            national_number = values[0]
            test_name = values[2]

            for test in self.pending_tests:
                if (test['national_number'] == national_number and
                    test['test_name'] == test_name):

                    self.selected_patient_id = test['patient_id']
                    self.selected_test_id = test['test_id']

                    # تحديث معلومات التحليل المحدد
                    info_text = f"المريض: {test['patient_name']} | التحليل: {test['test_name']} | الرقم الوطني: {test['national_number']}"
                    self.selected_info_label.config(text=info_text)

                    # تعبئة النتيجة الحالية إذا وجدت
                    if test['result']:
                        self.result_var.set(test['result'])
                        self.notes_var.set(test['notes'] or "")
                    else:
                        self.result_var.set("")
                        self.notes_var.set("")

                    break

    def on_test_double_click(self, event):
        """النقر المزدوج على تحليل"""
        self.on_test_select(event)
        # فتح نافذة تفاصيل أكثر أو التركيز على إدخال النتيجة
        if self.result_combo.get() == "":
            self.result_combo.focus()

    def set_quick_result(self, result: str):
        """تعيين نتيجة سريعة"""
        if not self.selected_patient_id or not self.selected_test_id:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل أولاً")
            return

        self.result_var.set(result)
        # حفظ النتيجة تلقائياً للنتائج السريعة
        self.save_result()

    def save_result(self):
        """حفظ النتيجة"""
        if not self.selected_patient_id or not self.selected_test_id:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل أولاً")
            return

        if not self.result_var.get():
            messagebox.showwarning("تحذير", "يرجى اختيار نتيجة")
            return

        try:
            # التحقق من وجود نتيجة سابقة
            existing_result = self.db_manager.execute_query(
                "SELECT id FROM results WHERE patient_id = ? AND test_id = ?",
                (self.selected_patient_id, self.selected_test_id)
            )

            if existing_result:
                # تحديث النتيجة الموجودة
                update_query = """
                    UPDATE results SET
                        result = ?, notes = ?, result_date = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                    WHERE patient_id = ? AND test_id = ?
                """
                params = (self.result_var.get(), self.notes_var.get(),
                         self.selected_patient_id, self.selected_test_id)

                if self.db_manager.execute_update(update_query, params):
                    messagebox.showinfo("نجح", "تم تحديث النتيجة بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث النتيجة")
            else:
                # إدراج نتيجة جديدة
                insert_query = """
                    INSERT INTO results (patient_id, test_id, result, notes)
                    VALUES (?, ?, ?, ?)
                """
                params = (self.selected_patient_id, self.selected_test_id,
                         self.result_var.get(), self.notes_var.get())

                if self.db_manager.execute_update(insert_query, params):
                    messagebox.showinfo("نجح", "تم حفظ النتيجة بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ النتيجة")

            # تحديث الجدول
            self.load_pending_tests()

            # مسح النموذج
            self.clear_result_form()

            # إشعار النوافذ الأخرى
            self.main_app.notify_observers('result_saved', {
                'patient_id': self.selected_patient_id,
                'test_id': self.selected_test_id,
                'result': self.result_var.get()
            })

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ النتيجة: {str(e)}")

    def delete_result(self):
        """حذف النتيجة"""
        if not self.selected_patient_id or not self.selected_test_id:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل أولاً")
            return

        # التحقق من وجود نتيجة
        existing_result = self.db_manager.execute_query(
            "SELECT id FROM results WHERE patient_id = ? AND test_id = ?",
            (self.selected_patient_id, self.selected_test_id)
        )

        if not existing_result:
            messagebox.showwarning("تحذير", "لا توجد نتيجة لحذفها")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه النتيجة؟"):
            if self.db_manager.execute_update(
                "DELETE FROM results WHERE patient_id = ? AND test_id = ?",
                (self.selected_patient_id, self.selected_test_id)
            ):
                messagebox.showinfo("نجح", "تم حذف النتيجة بنجاح")

                # تحديث الجدول
                self.load_pending_tests()

                # مسح النموذج
                self.clear_result_form()

                # إشعار النوافذ الأخرى
                self.main_app.notify_observers('result_deleted', {
                    'patient_id': self.selected_patient_id,
                    'test_id': self.selected_test_id
                })
            else:
                messagebox.showerror("خطأ", "فشل في حذف النتيجة")

    def clear_result_form(self):
        """مسح نموذج النتيجة"""
        self.selected_patient_id = None
        self.selected_test_id = None
        self.result_var.set("")
        self.notes_var.set("")
        self.selected_info_label.config(text="لم يتم اختيار تحليل")

    def bulk_result_entry(self):
        """إدخال نتائج متعددة"""
        # فتح نافذة إدخال النتائج المتعددة
        bulk_dialog = BulkResultDialog(self.window, self.db_manager, self.pending_tests)
        if bulk_dialog.results_saved:
            # تحديث الجدول
            self.load_pending_tests()

            # إشعار النوافذ الأخرى
            self.main_app.notify_observers('bulk_results_saved', {})

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
        self.main_app.update_status("تم تحديث البيانات")

    # دوال التصدير والطباعة
    def export_results(self):
        """تصدير النتائج إلى Excel"""
        try:
            from tkinter import filedialog
            import pandas as pd

            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف النتائج",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")]
            )

            if file_path:
                # إعداد البيانات للتصدير
                export_data = []
                for test in self.pending_tests:
                    export_data.append({
                        'الرقم الوطني': test['national_number'],
                        'اسم المريض': test['patient_name'],
                        'التحليل': test['test_name'],
                        'نوع العينة': test['sample_type'],
                        'تاريخ سحب العينة': test['sample_collection_date'],
                        'تاريخ استلام العينة': test['sample_received_date'],
                        'النتيجة': test['result'] if test['result'] else 'معلق',
                        'تاريخ النتيجة': test['result_date'] if test['result_date'] else '',
                        'ملاحظات': test['notes'] if test['notes'] else ''
                    })

                # تحويل إلى DataFrame وحفظ
                df = pd.DataFrame(export_data)
                df.to_excel(file_path, index=False, engine='openpyxl')

                messagebox.showinfo("نجح", f"تم تصدير {len(export_data)} نتيجة إلى Excel بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير النتائج: {str(e)}")

    def print_results(self):
        """طباعة النتائج"""
        try:
            from results_printer import ResultsPrinter

            # جمع النتائج المحددة أو جميع النتائج
            selected_items = self.pending_tree.selection()

            if selected_items:
                # طباعة النتائج المحددة
                results_to_print = []
                for item in selected_items:
                    values = self.pending_tree.item(item)['values']
                    national_number = values[0]
                    test_name = values[2]

                    for test in self.pending_tests:
                        if (test['national_number'] == national_number and
                            test['test_name'] == test_name):
                            results_to_print.append(test)
                            break
            else:
                # طباعة جميع النتائج المعروضة
                results_to_print = [test for test in self.pending_tests if test['result']]

            if not results_to_print:
                messagebox.showwarning("تحذير", "لا توجد نتائج للطباعة")
                return

            # طباعة النتائج
            printer = ResultsPrinter()
            printer.print_results(results_to_print)

            self.main_app.update_status(f"تم إرسال {len(results_to_print)} نتيجة للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة النتائج: {str(e)}")

    def show_quick_stats(self):
        """عرض إحصائيات سريعة"""
        # حساب الإحصائيات
        total_tests = len(self.pending_tests)
        pending_tests = len([t for t in self.pending_tests if not t['result']])
        completed_tests = total_tests - pending_tests

        positive_results = len([t for t in self.pending_tests if t['result'] == 'Positive'])
        negative_results = len([t for t in self.pending_tests if t['result'] == 'Negative'])
        retest_results = len([t for t in self.pending_tests if t['result'] in ['Retest', 'Recollection']])

        # عرض الإحصائيات
        stats_text = f"""
إحصائيات سريعة:

إجمالي التحاليل: {total_tests}
التحاليل المكتملة: {completed_tests}
التحاليل المعلقة: {pending_tests}

النتائج الإيجابية: {positive_results}
النتائج السلبية: {negative_results}
إعادة الفحص: {retest_results}

نسبة الإنجاز: {(completed_tests/total_tests*100):.1f}% إذا كان إجمالي التحاليل > 0 else 0
        """

        messagebox.showinfo("إحصائيات سريعة", stats_text)

    # دوال التزامن
    def on_data_changed(self, event_type: str, data: dict = None):
        """استقبال إشعارات التحديث من النوافذ الأخرى"""
        if event_type in ['patient_added', 'patient_updated', 'patient_deleted']:
            # إعادة تحميل التحاليل
            self.load_pending_tests()
        elif event_type in ['test_added', 'test_updated', 'test_deleted']:
            # إعادة تحميل قائمة التحاليل
            self.tests = self.db_manager.get_tests()
            test_names = [''] + [test['name'] for test in self.tests]
            self.test_filter_combo['values'] = test_names
            self.load_pending_tests()
        elif event_type in ['batch_created', 'batch_updated']:
            # إعادة تحميل التحاليل لأن هناك عينات جديدة قد تكون أضيفت
            self.load_pending_tests()

    def __del__(self):
        """تنظيف الموارد عند إغلاق النافذة"""
        try:
            if hasattr(self, 'main_app') and self in self.main_app.observers:
                self.main_app.observers.remove(self)
        except:
            pass


class BulkResultDialog:
    """نافذة إدخال النتائج المتعددة"""

    def __init__(self, parent, db_manager, pending_tests):
        self.db_manager = db_manager
        self.pending_tests = [t for t in pending_tests if not t['result']]  # فقط التحاليل المعلقة
        self.results_saved = False

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إدخال نتائج متعددة")
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.setup_interface()
        self.load_pending_tests()

        # انتظار النتيجة
        self.dialog.wait_window()

    def setup_interface(self):
        """إعداد واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        title_label = ttk.Label(main_frame, text="إدخال نتائج متعددة",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # إطار الجدول
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # جدول التحاليل المعلقة
        columns = ('اختيار', 'الرقم الوطني', 'اسم المريض', 'التحليل', 'النتيجة')
        self.tests_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        column_widths = [60, 100, 150, 120, 100]
        for i, col in enumerate(columns):
            self.tests_tree.heading(col, text=col)
            self.tests_tree.column(col, width=column_widths[i], anchor=tk.CENTER)

        # شريط التمرير
        tree_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tests_tree.yview)
        self.tests_tree.config(yscrollcommand=tree_scrollbar.set)

        self.tests_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار العمليات السريعة
        quick_frame = ttk.LabelFrame(main_frame, text="عمليات سريعة", padding="10")
        quick_frame.pack(fill=tk.X, pady=(0, 10))

        # أزرار النتائج السريعة
        quick_buttons = ttk.Frame(quick_frame)
        quick_buttons.pack(fill=tk.X)

        ttk.Label(quick_buttons, text="تطبيق على المحدد:").pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(quick_buttons, text="سلبي",
                  command=lambda: self.apply_bulk_result('Negative')).pack(side=tk.LEFT, padx=5)
        ttk.Button(quick_buttons, text="إيجابي",
                  command=lambda: self.apply_bulk_result('Positive')).pack(side=tk.LEFT, padx=5)
        ttk.Button(quick_buttons, text="إعادة فحص",
                  command=lambda: self.apply_bulk_result('Retest')).pack(side=tk.LEFT, padx=5)
        ttk.Button(quick_buttons, text="إعادة سحب",
                  command=lambda: self.apply_bulk_result('Recollection')).pack(side=tk.LEFT, padx=5)

        # أزرار التحكم
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X)

        ttk.Button(control_frame, text="تحديد الكل",
                  command=self.select_all).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="إلغاء التحديد",
                  command=self.deselect_all).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="حفظ النتائج",
                  command=self.save_bulk_results,
                  style='Accent.TButton').pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(control_frame, text="إلغاء",
                  command=self.cancel).pack(side=tk.RIGHT)

        # ربط الأحداث
        self.tests_tree.bind('<Button-1>', self.on_tree_click)

    def load_pending_tests(self):
        """تحميل التحاليل المعلقة"""
        for test in self.pending_tests:
            self.tests_tree.insert('', tk.END, values=(
                '☐',  # مربع اختيار فارغ
                test['national_number'],
                test['patient_name'],
                test['test_name'],
                ''  # النتيجة فارغة في البداية
            ))

    def on_tree_click(self, event):
        """عند النقر على الجدول"""
        region = self.tests_tree.identify_region(event.x, event.y)
        if region == "cell":
            column = self.tests_tree.identify_column(event.x, event.y)
            if column == '#1':  # عمود الاختيار
                item = self.tests_tree.identify_row(event.y)
                if item:
                    self.toggle_selection(item)

    def toggle_selection(self, item):
        """تبديل حالة الاختيار"""
        values = list(self.tests_tree.item(item)['values'])
        if values[0] == '☐':
            values[0] = '☑'
        else:
            values[0] = '☐'
        self.tests_tree.item(item, values=values)

    def select_all(self):
        """تحديد جميع التحاليل"""
        for item in self.tests_tree.get_children():
            values = list(self.tests_tree.item(item)['values'])
            values[0] = '☑'
            self.tests_tree.item(item, values=values)

    def deselect_all(self):
        """إلغاء تحديد جميع التحاليل"""
        for item in self.tests_tree.get_children():
            values = list(self.tests_tree.item(item)['values'])
            values[0] = '☐'
            self.tests_tree.item(item, values=values)

    def apply_bulk_result(self, result):
        """تطبيق نتيجة على التحاليل المحددة"""
        for item in self.tests_tree.get_children():
            values = list(self.tests_tree.item(item)['values'])
            if values[0] == '☑':  # محدد
                values[4] = result  # تعيين النتيجة
                self.tests_tree.item(item, values=values)

    def save_bulk_results(self):
        """حفظ النتائج المتعددة"""
        saved_count = 0
        errors = []

        for i, item in enumerate(self.tests_tree.get_children()):
            values = self.tests_tree.item(item)['values']

            if values[0] == '☑' and values[4]:  # محدد وله نتيجة
                try:
                    test_data = self.pending_tests[i]

                    # إدراج النتيجة
                    insert_query = """
                        INSERT INTO results (patient_id, test_id, result)
                        VALUES (?, ?, ?)
                    """
                    params = (test_data['patient_id'], test_data['test_id'], values[4])

                    if self.db_manager.execute_update(insert_query, params):
                        saved_count += 1
                    else:
                        errors.append(f"فشل في حفظ نتيجة {values[2]} - {values[3]}")

                except Exception as e:
                    errors.append(f"خطأ في {values[2]} - {values[3]}: {str(e)}")

        # عرض النتائج
        if saved_count > 0:
            self.results_saved = True
            message = f"تم حفظ {saved_count} نتيجة بنجاح"

            if errors:
                message += f"\n\nأخطاء ({len(errors)}):\n" + "\n".join(errors[:5])
                if len(errors) > 5:
                    message += f"\n... و {len(errors) - 5} أخطاء أخرى"

            messagebox.showinfo("نتيجة الحفظ", message)
            self.dialog.destroy()
        else:
            if errors:
                messagebox.showerror("خطأ", "فشل في حفظ النتائج:\n" + "\n".join(errors[:5]))
            else:
                messagebox.showwarning("تحذير", "لم يتم تحديد أي نتائج للحفظ")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()
