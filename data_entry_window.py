# -*- coding: utf-8 -*-
"""
نافذة إدخال البيانات - مختبر الصحة العامة المركزي ذي قار
Data Entry Window - Central Public Health Laboratory
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date
import re
from typing import Dict, List, Any
import pandas as pd
from barcode_generator import BarcodeGenerator

class DataEntryWindow:
    def __init__(self, parent, db_manager, main_app):
        """تهيئة نافذة إدخال البيانات"""
        self.parent = parent
        self.db_manager = db_manager
        self.main_app = main_app
        self.barcode_generator = BarcodeGenerator()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_variables()
        self.setup_interface()
        self.load_reference_data()
        self.clear_form()
        
        # تسجيل كمراقب للتحديثات
        self.main_app.add_observer(self)
    
    def setup_window(self):
        """إعداد النافذة العصرية"""
        self.window.title("📝 إدخال البيانات - مختبر الصحة العامة المركزي")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)

        # تطبيق الثيم العصري
        self.setup_modern_theme()

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # تعيين خلفية عصرية
        self.window.configure(bg='#f8f9fa')

    def setup_modern_theme(self):
        """إعداد الثيم العصري للنافذة"""
        style = ttk.Style()

        # الألوان العصرية
        self.colors = {
            'primary': '#2c3e50',
            'secondary': '#3498db',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'light': '#ecf0f1',
            'dark': '#2c3e50',
            'white': '#ffffff',
            'background': '#f8f9fa',
            'card': '#ffffff',
            'border': '#dee2e6'
        }

        # تخصيص الأزرار
        style.configure('Modern.TButton',
                       background=self.colors['secondary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8),
                       font=('Segoe UI', 10, 'bold'))

        style.map('Modern.TButton',
                 background=[('active', '#2980b9')])

        # تخصيص الأزرار الرئيسية
        style.configure('Primary.TButton',
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10),
                       font=('Segoe UI', 11, 'bold'))

        # تخصيص أزرار النجاح
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8),
                       font=('Segoe UI', 10, 'bold'))

        # تخصيص الإطارات
        style.configure('Card.TLabelFrame',
                       background=self.colors['card'],
                       borderwidth=1,
                       relief='solid')

        style.configure('Card.TLabelFrame.Label',
                       background=self.colors['card'],
                       foreground=self.colors['primary'],
                       font=('Segoe UI', 12, 'bold'))
    
    def setup_variables(self):
        """إعداد المتغيرات"""
        self.name_var = tk.StringVar()
        self.age_var = tk.StringVar()
        self.gender_var = tk.StringVar()
        self.address_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        self.passport_var = tk.StringVar()
        self.receipt_var = tk.StringVar()
        self.sample_type_var = tk.StringVar()
        self.sending_entity_var = tk.StringVar()
        self.collection_date_var = tk.StringVar()
        self.national_number_var = tk.StringVar()
        self.barcode_var = tk.StringVar()
        
        # قوائم البيانات المرجعية
        self.sample_types = []
        self.sending_entities = []
        self.tests = []
        self.selected_tests = []
        
        # متغير للمريض المحدد للتعديل
        self.current_patient_id = None
    
    def setup_interface(self):
        """إعداد واجهة المستخدم العصرية"""
        # إطار رئيسي مع خلفية عصرية
        main_frame = tk.Frame(self.window, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # رأس النافذة
        self.setup_window_header(main_frame)

        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(main_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        # تقسيم إلى عمودين
        left_panel = tk.Frame(content_frame, bg=self.colors['background'])
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        right_panel = tk.Frame(content_frame, bg=self.colors['background'])
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # العمود الأيسر - نموذج الإدخال
        self.setup_input_form(left_panel)

        # العمود الأيمن - قائمة المرضى
        self.setup_patients_panel(right_panel)

    def setup_window_header(self, parent):
        """إعداد رأس النافذة"""
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # محتوى الرأس
        content = tk.Frame(header_frame, bg=self.colors['primary'])
        content.pack(expand=True, fill=tk.BOTH, padx=30, pady=20)

        # العنوان
        title_label = tk.Label(content,
                              text="📝 إدخال البيانات",
                              font=('Segoe UI', 20, 'bold'),
                              fg='white',
                              bg=self.colors['primary'])
        title_label.pack(side=tk.LEFT)

        # معلومات سريعة
        info_label = tk.Label(content,
                             text="إضافة مرضى وعينات جديدة",
                             font=('Segoe UI', 12),
                             fg='#bdc3c7',
                             bg=self.colors['primary'])
        info_label.pack(side=tk.LEFT, padx=(20, 0))

        # إحصائيات سريعة
        stats_frame = tk.Frame(content, bg=self.colors['primary'])
        stats_frame.pack(side=tk.RIGHT)

        # عدد المرضى اليوم
        today_count = self.get_today_patients_count()
        stats_label = tk.Label(stats_frame,
                              text=f"مرضى اليوم: {today_count}",
                              font=('Segoe UI', 11, 'bold'),
                              fg='#2ecc71',
                              bg=self.colors['primary'])
        stats_label.pack()

    def setup_input_form(self, parent):
        """إعداد نموذج الإدخال"""
        # إطار البيانات الأساسية
        basic_card = self.create_card(parent, "👤 البيانات الأساسية")
        self.setup_basic_fields(basic_card)

        # إطار التحاليل
        tests_card = self.create_card(parent, "🧪 التحاليل المطلوبة")
        self.setup_tests_section(tests_card)

        # إطار العمليات
        operations_card = self.create_card(parent, "⚡ العمليات")
        self.setup_operations_section(operations_card)

    def setup_patients_panel(self, parent):
        """إعداد لوحة المرضى"""
        # بطاقة البحث
        search_card = self.create_card(parent, "🔍 البحث والفلترة")
        self.setup_search_section(search_card)

        # بطاقة قائمة المرضى
        patients_card = self.create_card(parent, "📋 قائمة المرضى")
        self.setup_patients_list(patients_card)

    def create_card(self, parent, title):
        """إنشاء بطاقة عصرية"""
        # إطار البطاقة
        card_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        card_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # رأس البطاقة
        header_frame = tk.Frame(card_frame, bg=self.colors['light'], height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # عنوان البطاقة
        title_label = tk.Label(header_frame,
                              text=title,
                              font=('Segoe UI', 14, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['light'])
        title_label.pack(side=tk.LEFT, padx=20, pady=15)

        # محتوى البطاقة
        content_frame = tk.Frame(card_frame, bg=self.colors['card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        return content_frame

    def get_today_patients_count(self):
        """الحصول على عدد مرضى اليوم"""
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')
            result = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM patients WHERE DATE(created_at) = ?",
                (today,)
            )
            return result[0]['count'] if result else 0
        except:
            return 0
    
    def setup_basic_fields(self, parent):
        """إعداد الحقول الأساسية"""
        # الصف الأول
        row1 = ttk.Frame(parent)
        row1.pack(fill=tk.X, pady=5)
        
        # الاسم (إلزامي)
        ttk.Label(row1, text="الاسم *:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        name_entry = ttk.Entry(row1, textvariable=self.name_var, width=20)
        name_entry.grid(row=0, column=1, padx=(0, 20))
        
        # العمر (إلزامي)
        ttk.Label(row1, text="العمر *:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        age_entry = ttk.Entry(row1, textvariable=self.age_var, width=10)
        age_entry.grid(row=0, column=3, padx=(0, 20))
        
        # الجنس (إلزامي)
        ttk.Label(row1, text="الجنس *:").grid(row=0, column=4, sticky=tk.W, padx=(0, 5))
        gender_combo = ttk.Combobox(row1, textvariable=self.gender_var, values=['M', 'F'], 
                                   state='readonly', width=8)
        gender_combo.grid(row=0, column=5)
        
        # الصف الثاني
        row2 = ttk.Frame(parent)
        row2.pack(fill=tk.X, pady=5)
        
        # العنوان (إلزامي)
        ttk.Label(row2, text="العنوان *:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        address_entry = ttk.Entry(row2, textvariable=self.address_var, width=30)
        address_entry.grid(row=0, column=1, padx=(0, 20))
        
        # رقم الهاتف (إلزامي)
        ttk.Label(row2, text="رقم الهاتف *:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        phone_entry = ttk.Entry(row2, textvariable=self.phone_var, width=15)
        phone_entry.grid(row=0, column=3)
        
        # الصف الثالث
        row3 = ttk.Frame(parent)
        row3.pack(fill=tk.X, pady=5)
        
        # نوع العينة (إلزامي)
        ttk.Label(row3, text="نوع العينة *:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.sample_type_combo = ttk.Combobox(row3, textvariable=self.sample_type_var, 
                                             state='readonly', width=15)
        self.sample_type_combo.grid(row=0, column=1, padx=(0, 20))
        
        # جهة الإرسال (إلزامي)
        ttk.Label(row3, text="جهة الإرسال *:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.sending_entity_combo = ttk.Combobox(row3, textvariable=self.sending_entity_var, 
                                                state='readonly', width=20)
        self.sending_entity_combo.grid(row=0, column=3)
        
        # الصف الرابع
        row4 = ttk.Frame(parent)
        row4.pack(fill=tk.X, pady=5)
        
        # تاريخ سحب العينة (إلزامي)
        ttk.Label(row4, text="تاريخ سحب العينة *:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        collection_date_entry = ttk.Entry(row4, textvariable=self.collection_date_var, width=12)
        collection_date_entry.grid(row=0, column=1, padx=(0, 5))
        ttk.Label(row4, text="(YYYY-MM-DD)", font=('Arial', 8)).grid(row=0, column=2, padx=(0, 20))
        
        # رقم الجواز (اختياري)
        ttk.Label(row4, text="رقم الجواز:").grid(row=0, column=3, sticky=tk.W, padx=(0, 5))
        passport_entry = ttk.Entry(row4, textvariable=self.passport_var, width=15)
        passport_entry.grid(row=0, column=4, padx=(0, 20))
        
        # رقم الوصل (اختياري)
        ttk.Label(row4, text="رقم الوصل:").grid(row=0, column=5, sticky=tk.W, padx=(0, 5))
        receipt_entry = ttk.Entry(row4, textvariable=self.receipt_var, width=15)
        receipt_entry.grid(row=0, column=6)
        
        # الصف الخامس - البيانات التلقائية
        row5 = ttk.Frame(parent)
        row5.pack(fill=tk.X, pady=5)
        
        # الرقم الوطني (تلقائي)
        ttk.Label(row5, text="الرقم الوطني:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        national_entry = ttk.Entry(row5, textvariable=self.national_number_var, 
                                  state='readonly', width=10)
        national_entry.grid(row=0, column=1, padx=(0, 20))
        
        # الباركود (تلقائي)
        ttk.Label(row5, text="الباركود:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        barcode_entry = ttk.Entry(row5, textvariable=self.barcode_var, 
                                 state='readonly', width=15)
        barcode_entry.grid(row=0, column=3, padx=(0, 10))
        
        # زر طباعة الباركود
        ttk.Button(row5, text="طباعة الباركود", 
                  command=self.print_barcode).grid(row=0, column=4, padx=5)
    
    def setup_tests_section(self, parent):
        """إعداد قسم التحاليل"""
        # إطار اختيار التحاليل
        selection_frame = ttk.Frame(parent)
        selection_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(selection_frame, text="اختر التحاليل:").pack(anchor=tk.W)
        
        # قائمة التحاليل المتاحة
        tests_frame = ttk.Frame(selection_frame)
        tests_frame.pack(fill=tk.X, pady=5)
        
        self.tests_listbox = tk.Listbox(tests_frame, height=6, selectmode=tk.MULTIPLE)
        tests_scrollbar = ttk.Scrollbar(tests_frame, orient=tk.VERTICAL, command=self.tests_listbox.yview)
        self.tests_listbox.config(yscrollcommand=tests_scrollbar.set)
        
        self.tests_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tests_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار التحاليل
        tests_buttons = ttk.Frame(selection_frame)
        tests_buttons.pack(fill=tk.X, pady=5)
        
        ttk.Button(tests_buttons, text="إضافة التحاليل المحددة", 
                  command=self.add_selected_tests).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(tests_buttons, text="إزالة التحليل", 
                  command=self.remove_selected_test).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(tests_buttons, text="مسح جميع التحاليل", 
                  command=self.clear_all_tests).pack(side=tk.LEFT)
        
        # قائمة التحاليل المحددة
        ttk.Label(selection_frame, text="التحاليل المحددة:").pack(anchor=tk.W, pady=(10, 0))
        
        selected_frame = ttk.Frame(selection_frame)
        selected_frame.pack(fill=tk.X, pady=5)
        
        self.selected_tests_listbox = tk.Listbox(selected_frame, height=4)
        selected_scrollbar = ttk.Scrollbar(selected_frame, orient=tk.VERTICAL, 
                                          command=self.selected_tests_listbox.yview)
        self.selected_tests_listbox.config(yscrollcommand=selected_scrollbar.set)
        
        self.selected_tests_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        selected_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_operations_section(self, parent):
        """إعداد قسم العمليات العصري"""
        # العمليات الرئيسية
        main_ops_frame = tk.Frame(parent, bg=self.colors['card'])
        main_ops_frame.pack(fill=tk.X, pady=(0, 15))

        # تسمية العمليات الرئيسية
        main_ops_label = tk.Label(main_ops_frame,
                                 text="💾 العمليات الرئيسية:",
                                 font=('Segoe UI', 11, 'bold'),
                                 fg=self.colors['primary'],
                                 bg=self.colors['card'])
        main_ops_label.pack(anchor=tk.W, pady=(0, 10))

        # أزرار العمليات الرئيسية
        main_buttons_frame = tk.Frame(main_ops_frame, bg=self.colors['card'])
        main_buttons_frame.pack(fill=tk.X, pady=(0, 15))

        # حفظ
        save_btn = tk.Button(main_buttons_frame,
                            text="💾 حفظ",
                            font=('Segoe UI', 11, 'bold'),
                            bg=self.colors['success'],
                            fg='white',
                            relief='flat',
                            bd=0,
                            padx=20,
                            pady=10,
                            cursor='hand2',
                            command=self.save_patient)
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        # تعديل
        update_btn = tk.Button(main_buttons_frame,
                              text="✏️ تعديل",
                              font=('Segoe UI', 11, 'bold'),
                              bg=self.colors['primary'],
                              fg='white',
                              relief='flat',
                              bd=0,
                              padx=20,
                              pady=10,
                              cursor='hand2',
                              command=self.edit_patient)
        update_btn.pack(side=tk.LEFT, padx=(0, 10))

        # حذف
        delete_btn = tk.Button(main_buttons_frame,
                              text="🗑️ حذف",
                              font=('Segoe UI', 11, 'bold'),
                              bg=self.colors['danger'],
                              fg='white',
                              relief='flat',
                              bd=0,
                              padx=20,
                              pady=10,
                              cursor='hand2',
                              command=self.delete_patient)
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # جديد
        new_btn = tk.Button(main_buttons_frame,
                           text="📄 جديد",
                           font=('Segoe UI', 11, 'bold'),
                           bg=self.colors['warning'],
                           fg='white',
                           relief='flat',
                           bd=0,
                           padx=20,
                           pady=10,
                           cursor='hand2',
                           command=self.clear_form)
        new_btn.pack(side=tk.LEFT, padx=(0, 10))

        # طباعة الباركود
        print_btn = tk.Button(main_buttons_frame,
                             text="🖨️ طباعة",
                             font=('Segoe UI', 11, 'bold'),
                             bg=self.colors['secondary'],
                             fg='white',
                             relief='flat',
                             bd=0,
                             padx=20,
                             pady=10,
                             cursor='hand2',
                             command=self.print_barcode)
        print_btn.pack(side=tk.LEFT)
    
    def setup_patients_list(self, parent):
        """إعداد قائمة المرضى العصرية"""
        # إطار الجدول مع تصميم عصري
        tree_frame = tk.Frame(parent, bg=self.colors['card'])
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الجدول مع تصميم عصري
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة',
                  'جهة الإرسال', 'تاريخ السحب', 'الباركود')

        # إطار للجدول مع حدود عصرية
        table_container = tk.Frame(tree_frame, bg='#dee2e6', bd=1, relief='solid')
        table_container.pack(fill=tk.BOTH, expand=True)

        self.patients_tree = ttk.Treeview(table_container, columns=columns, show='headings', height=12)

        # تخصيص مظهر الجدول
        style = ttk.Style()
        style.configure('Modern.Treeview',
                       background='white',
                       foreground=self.colors['primary'],
                       fieldbackground='white',
                       borderwidth=0,
                       font=('Segoe UI', 10))

        style.configure('Modern.Treeview.Heading',
                       background=self.colors['primary'],
                       foreground='white',
                       font=('Segoe UI', 11, 'bold'),
                       borderwidth=0)

        # تطبيق الستايل
        self.patients_tree.configure(style='Modern.Treeview')

        # تعيين عناوين الأعمدة مع أيقونات
        column_icons = {
            'الرقم الوطني': '🆔',
            'الاسم': '👤',
            'العمر': '📅',
            'الجنس': '⚧',
            'نوع العينة': '🧪',
            'جهة الإرسال': '🏥',
            'تاريخ السحب': '📆',
            'الباركود': '📊'
        }

        column_widths = [120, 150, 80, 80, 120, 150, 120, 120]

        for i, col in enumerate(columns):
            icon = column_icons.get(col, '')
            self.patients_tree.heading(col, text=f"{icon} {col}")
            self.patients_tree.column(col, width=column_widths[i], anchor=tk.CENTER)

        # شريط التمرير عصري
        tree_scrollbar_y = ttk.Scrollbar(table_container, orient=tk.VERTICAL, command=self.patients_tree.yview)
        tree_scrollbar_x = ttk.Scrollbar(table_container, orient=tk.HORIZONTAL, command=self.patients_tree.xview)
        self.patients_tree.config(yscrollcommand=tree_scrollbar_y.set, xscrollcommand=tree_scrollbar_x.set)

        # تخطيط الجدول
        self.patients_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)
        tree_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # ربط الأحداث
        self.patients_tree.bind('<Double-1>', self.on_patient_double_click)
        self.patients_tree.bind('<Button-1>', self.on_patient_select)

    def setup_search_section(self, parent):
        """إعداد قسم البحث العصري"""
        # حقل البحث الرئيسي
        search_frame = tk.Frame(parent, bg=self.colors['card'])
        search_frame.pack(fill=tk.X, pady=(0, 15))

        # تسمية البحث
        search_label = tk.Label(search_frame,
                               text="🔍 البحث السريع:",
                               font=('Segoe UI', 11, 'bold'),
                               fg=self.colors['primary'],
                               bg=self.colors['card'])
        search_label.pack(anchor=tk.W, pady=(0, 8))

        # إطار حقل البحث
        search_input_frame = tk.Frame(search_frame, bg=self.colors['card'])
        search_input_frame.pack(fill=tk.X, pady=(0, 10))

        # حقل البحث مع تصميم عصري
        search_entry = tk.Entry(search_input_frame,
                               textvariable=self.search_var,
                               font=('Segoe UI', 11),
                               relief='flat',
                               bd=5,
                               bg='#f8f9fa',
                               fg=self.colors['primary'])
        search_entry.pack(fill=tk.X, ipady=8)
        search_entry.bind('<KeyRelease>', self.on_search_changed)

        # أزرار البحث
        search_buttons_frame = tk.Frame(search_frame, bg=self.colors['card'])
        search_buttons_frame.pack(fill=tk.X, pady=(0, 15))

        # زر البحث
        search_btn = tk.Button(search_buttons_frame,
                              text="🔍 بحث",
                              font=('Segoe UI', 10, 'bold'),
                              bg=self.colors['secondary'],
                              fg='white',
                              relief='flat',
                              bd=0,
                              padx=15,
                              pady=8,
                              cursor='hand2',
                              command=self.search_patients)
        search_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر المسح
        clear_btn = tk.Button(search_buttons_frame,
                             text="🗑️ مسح",
                             font=('Segoe UI', 10, 'bold'),
                             bg=self.colors['warning'],
                             fg='white',
                             relief='flat',
                             bd=0,
                             padx=15,
                             pady=8,
                             cursor='hand2',
                             command=self.clear_search)
        clear_btn.pack(side=tk.LEFT)

        # خط فاصل
        separator = tk.Frame(search_frame, bg=self.colors['border'], height=1)
        separator.pack(fill=tk.X, pady=15)

        # عمليات المرضى
        operations_label = tk.Label(search_frame,
                                   text="⚙️ عمليات المرضى:",
                                   font=('Segoe UI', 11, 'bold'),
                                   fg=self.colors['primary'],
                                   bg=self.colors['card'])
        operations_label.pack(anchor=tk.W, pady=(0, 10))

        # أزرار العمليات
        ops_frame = tk.Frame(search_frame, bg=self.colors['card'])
        ops_frame.pack(fill=tk.X)

        # تعديل
        edit_btn = tk.Button(ops_frame,
                            text="✏️ تعديل",
                            font=('Segoe UI', 9, 'bold'),
                            bg=self.colors['primary'],
                            fg='white',
                            relief='flat',
                            bd=0,
                            padx=12,
                            pady=6,
                            cursor='hand2',
                            command=self.edit_patient)
        edit_btn.pack(side=tk.LEFT, padx=(0, 8))

        # حذف
        delete_btn = tk.Button(ops_frame,
                              text="🗑️ حذف",
                              font=('Segoe UI', 9, 'bold'),
                              bg=self.colors['danger'],
                              fg='white',
                              relief='flat',
                              bd=0,
                              padx=12,
                              pady=6,
                              cursor='hand2',
                              command=self.delete_patient)
        delete_btn.pack(side=tk.LEFT, padx=(0, 8))

        # استيراد
        import_btn = tk.Button(ops_frame,
                              text="📥 استيراد",
                              font=('Segoe UI', 9, 'bold'),
                              bg=self.colors['success'],
                              fg='white',
                              relief='flat',
                              bd=0,
                              padx=12,
                              pady=6,
                              cursor='hand2',
                              command=self.import_from_excel)
        import_btn.pack(side=tk.LEFT, padx=(0, 8))

        # تصدير
        export_btn = tk.Button(ops_frame,
                              text="📤 تصدير",
                              font=('Segoe UI', 9, 'bold'),
                              bg='#6c757d',
                              fg='white',
                              relief='flat',
                              bd=0,
                              padx=12,
                              pady=6,
                              cursor='hand2',
                              command=self.export_to_excel)
        export_btn.pack(side=tk.LEFT)

    def load_reference_data(self):
        """تحميل البيانات المرجعية"""
        # تحميل أنواع العينات
        self.sample_types = self.db_manager.get_sample_types()
        sample_type_names = [item['name'] for item in self.sample_types]
        self.sample_type_combo['values'] = sample_type_names

        # تحميل جهات الإرسال
        self.sending_entities = self.db_manager.get_sending_entities()
        sending_entity_names = [item['name'] for item in self.sending_entities]
        self.sending_entity_combo['values'] = sending_entity_names

        # تحميل التحاليل
        self.tests = self.db_manager.get_tests()
        self.tests_listbox.delete(0, tk.END)
        for test in self.tests:
            self.tests_listbox.insert(tk.END, test['name'])

        # تحميل قائمة المرضى
        self.load_patients_list()

    def clear_form(self):
        """مسح النموذج"""
        # مسح جميع الحقول
        self.name_var.set("")
        self.age_var.set("")
        self.gender_var.set("")
        self.address_var.set("")
        self.phone_var.set("")
        self.passport_var.set("")
        self.receipt_var.set("")
        self.sample_type_var.set("")
        self.sending_entity_var.set("")
        self.collection_date_var.set(datetime.now().strftime('%Y-%m-%d'))

        # تعيين الرقم الوطني والباركود التلقائي
        next_national = self.db_manager.get_next_national_number()
        self.national_number_var.set(str(next_national))
        self.barcode_var.set(self.db_manager.generate_barcode(next_national))

        # مسح التحاليل المحددة
        self.selected_tests = []
        self.selected_tests_listbox.delete(0, tk.END)

        # إعادة تعيين المريض الحالي
        self.current_patient_id = None

        self.main_app.update_status("تم مسح النموذج")

    def validate_form(self) -> bool:
        """التحقق من صحة البيانات"""
        errors = []

        # التحقق من الحقول الإلزامية
        if not self.name_var.get().strip():
            errors.append("الاسم مطلوب")

        if not self.age_var.get().strip():
            errors.append("العمر مطلوب")
        else:
            try:
                age = int(self.age_var.get())
                if age < 0 or age > 150:
                    errors.append("العمر يجب أن يكون بين 0 و 150")
            except ValueError:
                errors.append("العمر يجب أن يكون رقماً")

        if not self.gender_var.get():
            errors.append("الجنس مطلوب")

        if not self.address_var.get().strip():
            errors.append("العنوان مطلوب")

        if not self.phone_var.get().strip():
            errors.append("رقم الهاتف مطلوب")
        else:
            # التحقق من صيغة رقم الهاتف
            phone = self.phone_var.get().strip()
            if not re.match(r'^[\d\-\+\(\)\s]+$', phone):
                errors.append("صيغة رقم الهاتف غير صحيحة")

        if not self.sample_type_var.get():
            errors.append("نوع العينة مطلوب")

        if not self.sending_entity_var.get():
            errors.append("جهة الإرسال مطلوبة")

        if not self.collection_date_var.get():
            errors.append("تاريخ سحب العينة مطلوب")
        else:
            try:
                datetime.strptime(self.collection_date_var.get(), '%Y-%m-%d')
            except ValueError:
                errors.append("صيغة التاريخ غير صحيحة (YYYY-MM-DD)")

        if not self.selected_tests:
            errors.append("يجب اختيار تحليل واحد على الأقل")

        if errors:
            messagebox.showerror("خطأ في البيانات", "\n".join(errors))
            return False

        return True

    def save_patient(self):
        """حفظ بيانات المريض"""
        if not self.validate_form():
            return

        try:
            # الحصول على معرفات البيانات المرجعية
            sample_type_id = None
            for item in self.sample_types:
                if item['name'] == self.sample_type_var.get():
                    sample_type_id = item['id']
                    break

            sending_entity_id = None
            for item in self.sending_entities:
                if item['name'] == self.sending_entity_var.get():
                    sending_entity_id = item['id']
                    break

            if self.current_patient_id:
                # تحديث مريض موجود
                self.update_existing_patient(sample_type_id, sending_entity_id)
            else:
                # إضافة مريض جديد
                self.add_new_patient(sample_type_id, sending_entity_id)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

    def add_new_patient(self, sample_type_id: int, sending_entity_id: int):
        """إضافة مريض جديد"""
        # إدراج بيانات المريض
        patient_query = """
            INSERT INTO patients (
                national_number, name, age, gender, address, phone,
                passport_number, receipt_number, sample_type_id,
                sending_entity_id, sample_collection_date, barcode
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        patient_params = (
            int(self.national_number_var.get()),
            self.name_var.get().strip(),
            int(self.age_var.get()),
            self.gender_var.get(),
            self.address_var.get().strip(),
            self.phone_var.get().strip(),
            self.passport_var.get().strip() or None,
            self.receipt_var.get().strip() or None,
            sample_type_id,
            sending_entity_id,
            self.collection_date_var.get(),
            self.barcode_var.get()
        )

        if self.db_manager.execute_update(patient_query, patient_params):
            # الحصول على معرف المريض الجديد
            patient_id_result = self.db_manager.execute_query(
                "SELECT id FROM patients WHERE national_number = ?",
                (int(self.national_number_var.get()),)
            )

            if patient_id_result:
                patient_id = patient_id_result[0]['id']

                # إضافة التحاليل
                self.add_patient_tests(patient_id)

                # طباعة الباركود تلقائياً
                self.print_barcode()

                messagebox.showinfo("نجح", "تم حفظ بيانات المريض بنجاح")

                # تحديث القوائم
                self.load_patients_list()
                self.clear_form()

                # إشعار النوافذ الأخرى
                self.main_app.notify_observers('patient_added', {'patient_id': patient_id})
            else:
                messagebox.showerror("خطأ", "فشل في الحصول على معرف المريض")
        else:
            messagebox.showerror("خطأ", "فشل في حفظ بيانات المريض")

    def update_existing_patient(self, sample_type_id: int, sending_entity_id: int):
        """تحديث مريض موجود"""
        # تحديث بيانات المريض
        patient_query = """
            UPDATE patients SET
                name = ?, age = ?, gender = ?, address = ?, phone = ?,
                passport_number = ?, receipt_number = ?, sample_type_id = ?,
                sending_entity_id = ?, sample_collection_date = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """

        patient_params = (
            self.name_var.get().strip(),
            int(self.age_var.get()),
            self.gender_var.get(),
            self.address_var.get().strip(),
            self.phone_var.get().strip(),
            self.passport_var.get().strip() or None,
            self.receipt_var.get().strip() or None,
            sample_type_id,
            sending_entity_id,
            self.collection_date_var.get(),
            self.current_patient_id
        )

        if self.db_manager.execute_update(patient_query, patient_params):
            # حذف التحاليل القديمة وإضافة الجديدة
            self.db_manager.execute_update(
                "DELETE FROM patient_tests WHERE patient_id = ?",
                (self.current_patient_id,)
            )
            self.add_patient_tests(self.current_patient_id)

            messagebox.showinfo("نجح", "تم تحديث بيانات المريض بنجاح")

            # تحديث القوائم
            self.load_patients_list()
            self.clear_form()

            # إشعار النوافذ الأخرى
            self.main_app.notify_observers('patient_updated', {'patient_id': self.current_patient_id})
        else:
            messagebox.showerror("خطأ", "فشل في تحديث بيانات المريض")

    def add_patient_tests(self, patient_id: int):
        """إضافة تحاليل المريض"""
        for test_name in self.selected_tests:
            # البحث عن معرف التحليل
            test_id = None
            for test in self.tests:
                if test['name'] == test_name:
                    test_id = test['id']
                    break

            if test_id:
                self.db_manager.execute_update(
                    "INSERT OR IGNORE INTO patient_tests (patient_id, test_id) VALUES (?, ?)",
                    (patient_id, test_id)
                )

    def edit_patient(self):
        """تعديل مريض محدد"""
        selected = self.patients_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض للتعديل")
            return

        self.load_patient_data(selected[0])

    def delete_patient(self):
        """حذف مريض محدد"""
        selected = self.patients_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المريض؟"):
            item = self.patients_tree.item(selected[0])
            national_number = item['values'][0]

            # الحصول على معرف المريض
            patient_result = self.db_manager.execute_query(
                "SELECT id FROM patients WHERE national_number = ?",
                (national_number,)
            )

            if patient_result:
                patient_id = patient_result[0]['id']

                if self.db_manager.execute_update("DELETE FROM patients WHERE id = ?", (patient_id,)):
                    messagebox.showinfo("نجح", "تم حذف المريض بنجاح")
                    self.load_patients_list()
                    self.clear_form()

                    # إشعار النوافذ الأخرى
                    self.main_app.notify_observers('patient_deleted', {'patient_id': patient_id})
                else:
                    messagebox.showerror("خطأ", "فشل في حذف المريض")

    def load_patients_list(self):
        """تحميل قائمة المرضى"""
        # مسح القائمة الحالية
        for item in self.patients_tree.get_children():
            self.patients_tree.delete(item)

        # تحميل البيانات
        query = """
            SELECT p.*, st.name as sample_type_name, se.name as sending_entity_name
            FROM patients p
            JOIN sample_types st ON p.sample_type_id = st.id
            JOIN sending_entities se ON p.sending_entity_id = se.id
            ORDER BY p.national_number DESC
        """

        patients = self.db_manager.execute_query(query)

        for patient in patients:
            self.patients_tree.insert('', tk.END, values=(
                patient['national_number'],
                patient['name'],
                patient['age'],
                'ذكر' if patient['gender'] == 'M' else 'أنثى',
                patient['sample_type_name'],
                patient['sending_entity_name'],
                patient['sample_collection_date'],
                patient['barcode']
            ))

    def on_patient_select(self, event):
        """عند اختيار مريض من القائمة"""
        pass

    def on_patient_double_click(self, event):
        """عند النقر المزدوج على مريض"""
        selected = self.patients_tree.selection()
        if selected:
            self.load_patient_data(selected[0])

    def load_patient_data(self, item_id):
        """تحميل بيانات مريض للتعديل"""
        item = self.patients_tree.item(item_id)
        national_number = item['values'][0]

        # الحصول على بيانات المريض
        patient_result = self.db_manager.execute_query(
            "SELECT * FROM patients WHERE national_number = ?",
            (national_number,)
        )

        if patient_result:
            patient = patient_result[0]
            self.current_patient_id = patient['id']

            # تعبئة النموذج
            self.name_var.set(patient['name'])
            self.age_var.set(str(patient['age']))
            self.gender_var.set(patient['gender'])
            self.address_var.set(patient['address'])
            self.phone_var.set(patient['phone'])
            self.passport_var.set(patient['passport_number'] or "")
            self.receipt_var.set(patient['receipt_number'] or "")
            self.collection_date_var.set(patient['sample_collection_date'])
            self.national_number_var.set(str(patient['national_number']))
            self.barcode_var.set(patient['barcode'])

            # تعيين نوع العينة
            for item in self.sample_types:
                if item['id'] == patient['sample_type_id']:
                    self.sample_type_var.set(item['name'])
                    break

            # تعيين جهة الإرسال
            for item in self.sending_entities:
                if item['id'] == patient['sending_entity_id']:
                    self.sending_entity_var.set(item['name'])
                    break

            # تحميل التحاليل
            self.load_patient_tests(patient['id'])

            self.main_app.update_status(f"تم تحميل بيانات المريض: {patient['name']}")

    def load_patient_tests(self, patient_id: int):
        """تحميل تحاليل المريض"""
        tests_result = self.db_manager.execute_query("""
            SELECT t.name FROM patient_tests pt
            JOIN tests t ON pt.test_id = t.id
            WHERE pt.patient_id = ?
        """, (patient_id,))

        self.selected_tests = [test['name'] for test in tests_result]

        # تحديث قائمة التحاليل المحددة
        self.selected_tests_listbox.delete(0, tk.END)
        for test_name in self.selected_tests:
            self.selected_tests_listbox.insert(tk.END, test_name)

    # دوال التحاليل
    def add_selected_tests(self):
        """إضافة التحاليل المحددة"""
        selected_indices = self.tests_listbox.curselection()

        for index in selected_indices:
            test_name = self.tests_listbox.get(index)
            if test_name not in self.selected_tests:
                self.selected_tests.append(test_name)
                self.selected_tests_listbox.insert(tk.END, test_name)

        self.main_app.update_status(f"تم إضافة {len(selected_indices)} تحليل")

    def remove_selected_test(self):
        """إزالة التحليل المحدد"""
        selected_indices = self.selected_tests_listbox.curselection()

        if selected_indices:
            index = selected_indices[0]
            test_name = self.selected_tests_listbox.get(index)

            self.selected_tests.remove(test_name)
            self.selected_tests_listbox.delete(index)

            self.main_app.update_status(f"تم إزالة التحليل: {test_name}")
        else:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل لإزالته")

    def clear_all_tests(self):
        """مسح جميع التحاليل"""
        self.selected_tests = []
        self.selected_tests_listbox.delete(0, tk.END)
        self.main_app.update_status("تم مسح جميع التحاليل")

    # دوال البحث
    def search_patients(self):
        """البحث في المرضى"""
        search_term = self.search_var.get().strip()
        if not search_term:
            self.load_patients_list()
            return

        # مسح القائمة الحالية
        for item in self.patients_tree.get_children():
            self.patients_tree.delete(item)

        # البحث
        query = """
            SELECT p.*, st.name as sample_type_name, se.name as sending_entity_name
            FROM patients p
            JOIN sample_types st ON p.sample_type_id = st.id
            JOIN sending_entities se ON p.sending_entity_id = se.id
            WHERE p.name LIKE ? OR p.national_number LIKE ? OR p.phone LIKE ? OR p.barcode LIKE ?
            ORDER BY p.national_number DESC
        """

        search_pattern = f"%{search_term}%"
        patients = self.db_manager.execute_query(query, (search_pattern, search_pattern, search_pattern, search_pattern))

        for patient in patients:
            self.patients_tree.insert('', tk.END, values=(
                patient['national_number'],
                patient['name'],
                patient['age'],
                'ذكر' if patient['gender'] == 'M' else 'أنثى',
                patient['sample_type_name'],
                patient['sending_entity_name'],
                patient['sample_collection_date'],
                patient['barcode']
            ))

        self.main_app.update_status(f"تم العثور على {len(patients)} نتيجة")

    def on_search_changed(self, event):
        """عند تغيير نص البحث"""
        # البحث التلقائي عند الكتابة
        self.window.after(500, self.search_patients)

    def clear_search(self):
        """مسح البحث"""
        self.search_var.set("")
        self.load_patients_list()
        self.main_app.update_status("تم مسح البحث")

    # دوال الاستيراد والتصدير
    def import_from_excel(self):
        """استيراد البيانات من Excel"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Excel",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )

        if file_path:
            try:
                # قراءة ملف Excel
                df = pd.read_excel(file_path)

                # التحقق من وجود الأعمدة المطلوبة
                required_columns = ['الاسم', 'العمر', 'الجنس', 'العنوان', 'رقم الهاتف',
                                  'نوع العينة', 'جهة الإرسال', 'تاريخ سحب العينة']

                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    messagebox.showerror("خطأ", f"الأعمدة التالية مفقودة: {', '.join(missing_columns)}")
                    return

                # استيراد البيانات
                imported_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # التحقق من البيانات
                        if pd.isna(row['الاسم']) or pd.isna(row['العمر']):
                            errors.append(f"الصف {index + 2}: بيانات ناقصة")
                            continue

                        # الحصول على معرفات البيانات المرجعية
                        sample_type_id = self.get_sample_type_id(row['نوع العينة'])
                        sending_entity_id = self.get_sending_entity_id(row['جهة الإرسال'])

                        if not sample_type_id or not sending_entity_id:
                            errors.append(f"الصف {index + 2}: نوع العينة أو جهة الإرسال غير موجودة")
                            continue

                        # إنشاء بيانات المريض
                        national_number = self.db_manager.get_next_national_number()
                        barcode = self.db_manager.generate_barcode(national_number)

                        patient_data = (
                            national_number,
                            str(row['الاسم']).strip(),
                            int(row['العمر']),
                            'M' if str(row['الجنس']).upper() in ['M', 'ذكر', 'MALE'] else 'F',
                            str(row['العنوان']).strip(),
                            str(row['رقم الهاتف']).strip(),
                            str(row.get('رقم الجواز', '')) if not pd.isna(row.get('رقم الجواز')) else None,
                            str(row.get('رقم الوصل', '')) if not pd.isna(row.get('رقم الوصل')) else None,
                            sample_type_id,
                            sending_entity_id,
                            str(row['تاريخ سحب العينة']),
                            barcode
                        )

                        # إدراج المريض
                        patient_query = """
                            INSERT INTO patients (
                                national_number, name, age, gender, address, phone,
                                passport_number, receipt_number, sample_type_id,
                                sending_entity_id, sample_collection_date, barcode
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """

                        if self.db_manager.execute_update(patient_query, patient_data):
                            imported_count += 1

                            # طباعة الباركود
                            self.barcode_generator.print_barcode(barcode, str(row['الاسم']))

                    except Exception as e:
                        errors.append(f"الصف {index + 2}: {str(e)}")

                # عرض النتائج
                message = f"تم استيراد {imported_count} مريض بنجاح"
                if errors:
                    message += f"\n\nأخطاء ({len(errors)}):\n" + "\n".join(errors[:10])
                    if len(errors) > 10:
                        message += f"\n... و {len(errors) - 10} أخطاء أخرى"

                messagebox.showinfo("نتيجة الاستيراد", message)

                # تحديث القائمة
                self.load_patients_list()

                # إشعار النوافذ الأخرى
                self.main_app.notify_observers('patients_imported', {'count': imported_count})

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في استيراد الملف: {str(e)}")

    def get_sample_type_id(self, name: str) -> int:
        """الحصول على معرف نوع العينة"""
        for item in self.sample_types:
            if item['name'] == name:
                return item['id']
        return None

    def get_sending_entity_id(self, name: str) -> int:
        """الحصول على معرف جهة الإرسال"""
        for item in self.sending_entities:
            if item['name'] == name:
                return item['id']
        return None

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ ملف Excel",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx")]
        )

        if file_path:
            try:
                # الحصول على البيانات
                query = """
                    SELECT p.national_number as 'الرقم الوطني',
                           p.name as 'الاسم',
                           p.age as 'العمر',
                           CASE WHEN p.gender = 'M' THEN 'ذكر' ELSE 'أنثى' END as 'الجنس',
                           p.address as 'العنوان',
                           p.phone as 'رقم الهاتف',
                           p.passport_number as 'رقم الجواز',
                           p.receipt_number as 'رقم الوصل',
                           st.name as 'نوع العينة',
                           se.name as 'جهة الإرسال',
                           p.sample_collection_date as 'تاريخ سحب العينة',
                           p.sample_received_date as 'تاريخ استلام العينة',
                           p.barcode as 'الباركود'
                    FROM patients p
                    JOIN sample_types st ON p.sample_type_id = st.id
                    JOIN sending_entities se ON p.sending_entity_id = se.id
                    ORDER BY p.national_number
                """

                patients = self.db_manager.execute_query(query)

                if patients:
                    # تحويل إلى DataFrame
                    df = pd.DataFrame(patients)

                    # حفظ إلى Excel
                    df.to_excel(file_path, index=False, engine='openpyxl')

                    messagebox.showinfo("نجح", f"تم تصدير {len(patients)} مريض إلى Excel بنجاح")
                else:
                    messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير الملف: {str(e)}")

    # دوال الطباعة
    def print_barcode(self):
        """طباعة الباركود"""
        if not self.barcode_var.get():
            messagebox.showwarning("تحذير", "لا يوجد باركود للطباعة")
            return

        try:
            patient_name = self.name_var.get() or "غير محدد"
            self.barcode_generator.print_barcode(self.barcode_var.get(), patient_name)
            self.main_app.update_status("تم إرسال الباركود للطباعة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة الباركود: {str(e)}")

    # دوال التزامن
    def on_data_changed(self, event_type: str, data: dict = None):
        """استقبال إشعارات التحديث من النوافذ الأخرى"""
        if event_type in ['sample_type_added', 'sample_type_updated', 'sample_type_deleted']:
            self.load_reference_data()
        elif event_type in ['sending_entity_added', 'sending_entity_updated', 'sending_entity_deleted']:
            self.load_reference_data()
        elif event_type in ['test_added', 'test_updated', 'test_deleted']:
            self.load_reference_data()
        elif event_type == 'patient_updated' and data and data.get('patient_id') == self.current_patient_id:
            # إعادة تحميل بيانات المريض إذا تم تحديثه من نافذة أخرى
            self.load_patients_list()

    def __del__(self):
        """تنظيف الموارد عند إغلاق النافذة"""
        try:
            if hasattr(self, 'main_app') and self in self.main_app.observers:
                self.main_app.observers.remove(self)
        except:
            pass
