# -*- coding: utf-8 -*-
"""
طابع النتائج - مختبر الصحة العامة المركزي ذي قار
Results Printer - Central Public Health Laboratory
"""

import os
import tempfile
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

class ResultsPrinter:
    def __init__(self):
        """تهيئة طابع النتائج"""
        self.temp_dir = tempfile.gettempdir()
        self.setup_fonts()
        self.setup_styles()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي
            arabic_fonts = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
                "C:/Windows/Fonts/calibri.ttf"
            ]
            
            for font_path in arabic_fonts:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    break
            else:
                self.arabic_font = 'Helvetica'
                return
            
            self.arabic_font = 'Arabic'
            
        except Exception as e:
            print(f"خطأ في تحميل الخط العربي: {e}")
            self.arabic_font = 'Helvetica'
    
    def setup_styles(self):
        """إعداد أنماط النص"""
        self.styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Title'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.black
        )
        
        # نمط العنوان الفرعي
        self.subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=self.styles['Heading2'],
            fontName=self.arabic_font,
            fontSize=14,
            alignment=TA_CENTER,
            spaceAfter=15,
            textColor=colors.darkblue
        )
        
        # نمط النص العادي
        self.normal_style = ParagraphStyle(
            'CustomNormal',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_RIGHT,
            spaceAfter=6
        )
        
        # نمط النص المتوسط
        self.center_style = ParagraphStyle(
            'CustomCenter',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_CENTER
        )
    
    def print_results(self, results_data: list):
        """طباعة النتائج"""
        try:
            # إنشاء ملف PDF مؤقت
            pdf_file = os.path.join(self.temp_dir, f"results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
            
            # إنشاء المستند
            doc = SimpleDocTemplate(
                pdf_file,
                pagesize=A4,
                rightMargin=0.5*inch,
                leftMargin=0.5*inch,
                topMargin=0.5*inch,
                bottomMargin=0.5*inch
            )
            
            # بناء محتوى المستند
            story = []
            
            # إضافة الرأس
            self.add_header(story)
            
            # إضافة جدول النتائج
            self.add_results_table(story, results_data)
            
            # إضافة التذييل
            self.add_footer(story)
            
            # بناء المستند
            doc.build(story)
            
            # فتح الملف للطباعة
            os.startfile(pdf_file, "print")
            
            return pdf_file
            
        except Exception as e:
            print(f"خطأ في طباعة النتائج: {e}")
            raise e
    
    def add_header(self, story):
        """إضافة رأس المستند"""
        # شعار وزارة الصحة (إذا توفر)
        try:
            logo_path = "ministry_logo.png"
            if os.path.exists(logo_path):
                logo = Image(logo_path, width=1*inch, height=1*inch)
                story.append(logo)
                story.append(Spacer(1, 10))
        except:
            pass
        
        # عنوان وزارة الصحة
        ministry_title = Paragraph("وزارة الصحة العراقية", self.title_style)
        story.append(ministry_title)
        
        # عنوان المختبر
        lab_title = Paragraph("مختبر الصحة العامة المركزي - ذي قار", self.subtitle_style)
        story.append(lab_title)
        
        # عنوان التقرير
        report_title = Paragraph("تقرير النتائج المختبرية", self.subtitle_style)
        story.append(report_title)
        
        story.append(Spacer(1, 20))
    
    def add_results_table(self, story, results_data):
        """إضافة جدول النتائج"""
        if not results_data:
            no_data = Paragraph("لا توجد نتائج للعرض", self.center_style)
            story.append(no_data)
            return
        
        # رأس الجدول
        table_data = [["ت", "الرقم الوطني", "اسم المريض", "التحليل", "النتيجة", "تاريخ النتيجة", "ملاحظات"]]
        
        # إضافة بيانات النتائج
        for i, result in enumerate(results_data, 1):
            # تحويل النتيجة إلى العربية
            result_text = self.translate_result(result['result'])
            
            # تنسيق التاريخ
            result_date = ""
            if result['result_date']:
                try:
                    date_obj = datetime.strptime(result['result_date'][:10], '%Y-%m-%d')
                    result_date = date_obj.strftime('%Y-%m-%d')
                except:
                    result_date = result['result_date'][:10]
            
            table_data.append([
                str(i),
                str(result['national_number']),
                result['patient_name'],
                result['test_name'],
                result_text,
                result_date,
                result['notes'] if result['notes'] else ''
            ])
        
        # إنشاء الجدول
        table = Table(table_data, colWidths=[0.5*inch, 1*inch, 1.5*inch, 1.5*inch, 1*inch, 1*inch, 1.5*inch])
        
        # تطبيق التنسيق
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))
        
        # تلوين النتائج حسب النوع
        for i, result in enumerate(results_data, 1):
            row = i  # الصف في الجدول (بدءاً من 1 لأن 0 هو الرأس)
            if result['result'] == 'Positive':
                table.setStyle(TableStyle([('BACKGROUND', (4, row), (4, row), colors.lightcoral)]))
            elif result['result'] == 'Negative':
                table.setStyle(TableStyle([('BACKGROUND', (4, row), (4, row), colors.lightgreen)]))
            elif result['result'] in ['Retest', 'Recollection']:
                table.setStyle(TableStyle([('BACKGROUND', (4, row), (4, row), colors.lightyellow)]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        # إضافة ملخص النتائج
        self.add_results_summary(story, results_data)
    
    def add_results_summary(self, story, results_data):
        """إضافة ملخص النتائج"""
        # حساب الإحصائيات
        total = len(results_data)
        positive = len([r for r in results_data if r['result'] == 'Positive'])
        negative = len([r for r in results_data if r['result'] == 'Negative'])
        retest = len([r for r in results_data if r['result'] in ['Retest', 'Recollection']])
        other = total - positive - negative - retest
        
        # جدول الملخص
        summary_title = Paragraph("ملخص النتائج", self.subtitle_style)
        story.append(summary_title)
        
        summary_data = [
            ["إجمالي النتائج", str(total)],
            ["النتائج الإيجابية", str(positive)],
            ["النتائج السلبية", str(negative)],
            ["إعادة الفحص", str(retest)],
            ["أخرى", str(other)]
        ]
        
        summary_table = Table(summary_data, colWidths=[2*inch, 1*inch])
        summary_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        
        story.append(summary_table)
    
    def add_footer(self, story):
        """إضافة تذييل المستند"""
        # تاريخ ووقت الطباعة
        print_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        footer_text = f"تم الطباعة في: {print_time}"
        footer = Paragraph(footer_text, self.center_style)
        
        story.append(Spacer(1, 30))
        story.append(footer)
        
        # مساحة للتوقيعات
        signature_text = """
        <br/><br/>
        توقيع المسؤول: __________________ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; التاريخ: __________________
        <br/><br/>
        توقيع المدير: __________________ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; التاريخ: __________________
        """
        
        signature = Paragraph(signature_text, self.normal_style)
        story.append(signature)
    
    def translate_result(self, result):
        """ترجمة النتيجة إلى العربية"""
        translations = {
            'Positive': 'إيجابي',
            'Negative': 'سلبي',
            'Retest': 'إعادة فحص',
            'Recollection': 'إعادة سحب',
            'Sent': 'مُرسل',
            'TND': 'غير محدد'
        }
        return translations.get(result, result)
    
    def print_single_result(self, result_data):
        """طباعة نتيجة واحدة (تقرير فردي)"""
        try:
            # إنشاء ملف PDF مؤقت
            pdf_file = os.path.join(self.temp_dir, f"result_{result_data['national_number']}.pdf")
            
            # إنشاء المستند
            doc = SimpleDocTemplate(
                pdf_file,
                pagesize=A4,
                rightMargin=0.5*inch,
                leftMargin=0.5*inch,
                topMargin=0.5*inch,
                bottomMargin=0.5*inch
            )
            
            # بناء محتوى المستند
            story = []
            
            # إضافة الرأس
            self.add_header(story)
            
            # إضافة بيانات المريض
            self.add_patient_info(story, result_data)
            
            # إضافة النتيجة
            self.add_single_result_info(story, result_data)
            
            # إضافة التذييل
            self.add_footer(story)
            
            # بناء المستند
            doc.build(story)
            
            # فتح الملف للطباعة
            os.startfile(pdf_file, "print")
            
            return pdf_file
            
        except Exception as e:
            print(f"خطأ في طباعة النتيجة الفردية: {e}")
            raise e
    
    def add_patient_info(self, story, result_data):
        """إضافة معلومات المريض"""
        patient_title = Paragraph("معلومات المريض", self.subtitle_style)
        story.append(patient_title)
        
        patient_info = [
            ["الاسم:", result_data['patient_name']],
            ["الرقم الوطني:", str(result_data['national_number'])],
            ["نوع العينة:", result_data['sample_type']],
            ["تاريخ سحب العينة:", result_data['sample_collection_date']],
            ["تاريخ استلام العينة:", result_data['sample_received_date'][:10]]
        ]
        
        patient_table = Table(patient_info, colWidths=[2*inch, 3*inch])
        patient_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        
        story.append(patient_table)
        story.append(Spacer(1, 20))
    
    def add_single_result_info(self, story, result_data):
        """إضافة معلومات النتيجة الفردية"""
        result_title = Paragraph("نتيجة التحليل", self.subtitle_style)
        story.append(result_title)
        
        result_text = self.translate_result(result_data['result'])
        result_date = result_data['result_date'][:10] if result_data['result_date'] else ''
        
        result_info = [
            ["التحليل:", result_data['test_name']],
            ["النتيجة:", result_text],
            ["تاريخ النتيجة:", result_date],
            ["ملاحظات:", result_data['notes'] if result_data['notes'] else 'لا توجد']
        ]
        
        result_table = Table(result_info, colWidths=[2*inch, 3*inch])
        result_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        
        # تلوين النتيجة
        if result_data['result'] == 'Positive':
            result_table.setStyle(TableStyle([('BACKGROUND', (1, 1), (1, 1), colors.lightcoral)]))
        elif result_data['result'] == 'Negative':
            result_table.setStyle(TableStyle([('BACKGROUND', (1, 1), (1, 1), colors.lightgreen)]))
        elif result_data['result'] in ['Retest', 'Recollection']:
            result_table.setStyle(TableStyle([('BACKGROUND', (1, 1), (1, 1), colors.lightyellow)]))
        
        story.append(result_table)
